# Changelog

## [0.10.1] - 2025-8-24
### 🌟 主要功能更改
- planner现在改为大小核结构，移除激活阶段，提高回复速度和动作调用精准度
- 优化关系的表现的效率

- 优化识图的表现
- 为planner添加单独控制的提示词
- 修复激活值计算异常的BUG
- 修复lpmm日志错误
- 修复首句不回复的问题
- 修复emoji管理器的一个BUG
- 优化对模型请求的处理
- 重构内部代码
- 暂时禁用记忆


## [0.10.0] - 2025-8-18
### 🌟 主要功能更改
- 优化的回复生成，现在的回复对上下文把控更加精准
- 新的回复逻辑控制，现在合并了normal和focus模式，更加统一
- 优化表达方式系统，现在学习和使用更加精准
- 新的关系系统，现在的关系构建更精准也更克制
- 工具系统重构，现在合并到了插件系统中
- 彻底重构了整个LLM Request了，现在支持模型轮询和更多灵活的参数
  - 同时重构了整个模型配置系统，升级需要重新配置llm配置文件
- **警告所有插件开发者：插件系统即将迎来不稳定时期，随时会发动更改。**

#### 🔧 工具系统重构
- **工具系统整合**: 工具系统现在完全合并到插件系统中，提供统一的扩展能力
- **工具启用控制**: 支持配置是否启用特定工具，提供更人性化的直接调用方式
- **配置文件读取**: 工具现在支持读取配置文件，增强配置灵活性

#### 🚀 LLM系统全面重构
- **LLM Request重构**: 彻底重构了整个LLM Request系统，现在支持模型轮询和更多灵活的参数
- **模型配置升级**: 同时重构了整个模型配置系统，升级需要重新配置llm配置文件
- **任务类型支持**: 新增任务类型和能力字段至模型配置，增强模型初始化逻辑
- **异常处理增强**: 增强LLMRequest类的异常处理，添加统一的模型异常处理方法

#### 🔌 插件系统稳定化
- **插件系统重构完成**: 随着LLM Request的重构，插件系统彻底重构完成，进入稳定状态
- **API扩展**: 仅增加新的API，保持向后兼容性
- **插件管理优化**: 让插件管理配置真正有用，提升管理体验

#### 💾 记忆系统优化
- **及时构建**: 记忆系统再优化，现在及时构建，并且不会重复构建
- **精确提取**: 记忆提取更精确，提升记忆质量

#### 🎭 表达方式系统
- **表达方式记录**: 记录使用的表达方式，提供更好的学习追踪
- **学习优化**: 优化表达方式提取，修复表达学习出错问题
- **配置优化**: 优化表达方式配置和逻辑，提升系统稳定性

#### 🔄 聊天系统统一
- **normal和focus合并**: 彻底合并normal和focus，完全基于planner决定target message
- **no_reply内置**: 将no_reply功能移动到主循环中，简化系统架构
- **回复优化**: 优化reply，填补缺失值，让麦麦可以回复自己的消息
- **频率控制API**: 加入聊天频率控制相关API，提供更精细的控制

#### 日志系统改进
- **日志颜色优化**: 修改了log的颜色，更加护眼
- **日志清理优化**: 修复了日志清理先等24h的问题，提升系统性能
- **计时定位**: 通过计时定位LLM异常延时，提升问题排查效率

### 🐛 问题修复

#### 代码质量提升
- **lint问题修复**: 修复了lint爆炸的问题，代码更加规范了
- **导入优化**: 修复导入爆炸和文档错误，优化代码结构

#### 系统稳定性
- **循环导入**: 修复了import时循环导入的问题
- **并行动作**: 修复并行动作炸裂问题，提升并发处理能力
- **空响应处理**: 空响应就raise，避免系统异常

#### 功能修复
- **API问题**: 修复api问题，提升系统可用性
- **notice问题**: 为组件方法提供新参数，暂时解决notice问题
- **关系构建**: 修复不认识的用户构建关系问题
- **流式解析**: 修复流式解析越界问题，避免空choices的SSE帧错误

#### 配置和兼容性
- **默认值**: 添加默认值，提升配置灵活性
- **类型问题**: 修复类型问题，提升代码健壮性
- **配置加载**: 优化配置加载逻辑，提升系统启动稳定性


## [0.9.1] - 2025-7-26

### 主要修复和优化

- 优化回复意愿
- 优化专注模式回复频率
- 优化关键词提取
- 修复部分模型产生的400问题

### 细节优化

- 修复reply导致的planner异常空跳
- 修复表达方式迁移空目录问题
- 修复reply_to空字段问题
- 无可用动作导致的空plan问题
- 修复人格未压缩导致产生句号分割
- 将metioned bot 和 at应用到focus prompt中
- 更好的兴趣度计算
- 修复部分模型由于enable_thinking导致的400问题
- 移除dependency_manager


## [0.9.0] - 2025-7-24

### 摘要
MaiBot 0.9.0 重磅升级！本版本带来两大核心突破：**全面重构的插件系统**提供更强大的扩展能力和管理功能；**normal和focus模式统一化处理**大幅简化架构并提升性能。同时新增s4u prompt模式优化、语音消息支持、全新情绪系统和mais4u直播互动功能，为MaiBot带来更自然、更智能的交互体验！

### 🌟 主要功能概览

#### 🔌 插件系统全面重构 - 重点升级
- **完整管理API**: 全新的插件管理API，支持插件的启用、禁用、重载和卸载操作
- **权限控制系统**: 为插件管理增加完善的权限控制，确保系统安全性
- **智能依赖管理**: 优化插件依赖管理和自动注册机制，减少配置复杂度

#### ⚡ Normal和Focus模式统一化处理 - 重点升级
- **架构统一**: 彻底统一normal和focus聊天模式，消除模式间的差异和复杂性
- **智能模式切换**: 优化频率控制和模式切换逻辑，normal可以无缝切换到focus
- **统一LLM激活**: normal模式现在支持LLM激活插件，与focus模式功能对等
- **一致的关系构建**: normal采用与focus一致的关系构建机制，提升交互质量
- **统一退出机制**: 为focus提供更合理的退出方法，简化状态管理

#### 🎯 s4u prompt模式
- **s4u prompt模式**: 新增专门的s4u prompt构建方式，提供更好的交互效果
- **配置化启用**: 可在配置文件中选择启用s4u prompt模式，灵活控制
- **兼容性保持**: 与现有系统完全兼容，可随时切换启用或禁用

#### 🎤 语音消息支持
- **Voice消息处理**: 新增对voice类型消息的支持，麦麦现在可以识别和处理语音消息（需要模型配置）

#### 全新情绪系统
- **持续情绪**: 麦麦现在拥有持续的情绪状态，情绪会影响回复风格和行为


### 💻 更新预览

#### 关系系统优化
- **prompt优化**: 优化关系prompt和person_info信息展示
- **构建间隔**: 让关系构建间隔可配置，提升灵活性
- **关系配置**: 优化关系配置，采用和focus一致的关系构建

#### 表情包系统升级
- **识别增强**: 加强emoji的识别能力，优化emoji显示
- **匹配精准**: 更精准的表情包匹配算法

#### 完善mais4u系统（需要amaidesu支持）
- **直播互动**: 新增mais4u直播功能，支持实时互动和思考状态展示
- **动作控制**: 支持眨眼、微动作、注视等多种动作适配

#### 日志系统优化
- **显示优化**: 优化Logger前缀映射、颜色格式和计时信息显示
- **级别优化**: 优化日志级别和信息过滤，提升调试体验
- **日志查看器**: 升级logger_viewer，移除无用脚本

#### 配置系统改进
- **配置简化**: 简化配置文件，让配置更加精简易懂
- **prompt显示**: 可选打开prompt显示功能
- **配置更新**: 更好的配置文件更新机制和更新内容显示

#### 问题修复与优化

- 修复normal planner没有超时退出问题，添加回复超时检查
- 重构no_action逻辑，不再使用小模型，采用激活度决定
- 修复图片与文字混合兴趣值为0的情况
- 适配无兴趣度消息处理
- 优化Docker镜像构建流程，合并AMD64和ARM64构建步骤
- 移除vtb插件和take_picture_plugin，功能已由其他系统接管,移除pfc遗留代码和其他过时功能
- 移除observation和processor等冗余组件，大幅简化focus代码逻辑
- 修复了LPMM的学习问题


## [0.8.1] - 2025-7-5

功能更新:

- normal现在和focus一样支持tool
- focus现在和normal一样每次调用lpmm
- 移除人格表达

优化和修复：

- 修复表情包配置无效问题
- 合并normal和focus的prompt构建
- 非TTY环境禁用console_input_loop
- 修复过滤消息仍被存储至数据库的问题
- 私聊强制开启focus模式
- 支持解析reply_to和at
- 修复focus冷却时间导致的固定沉默
- 移除豆包画图插件，此插件现在插件广场提供
- 修复表达器无法读取原始文本
- 修复normal planner没有超时退出问题

## [0.8.0] - 2025-6-27

MaiBot 0.8.0 现已推出！

### **主要升级点：**

1.插件系统正式加入，现已上线插件商店，同时支持normal和focus
2.大幅降低了token消耗，更省钱
3.加入人物印象系统，麦麦可以对群友有不同的印象
4.可以精细化控制不同时段和不同群聊的发言频率

#### 其他升级

日志系统重构使用structlog
大量稳定性修复和性能优化。
MMC启动速度加快

### 🔌 插系统正式推出
**全面重构的插件生态系统，支持强大 的扩展能力**

- **插件API重构**: 全面重构插件系统，统一加载机制，区分内部插件和外部插件
- **插件仓库**：现可以分享和下载插件
- **依赖管理**: 新增插件依赖管理系统，支持自动注册和依赖检查
- **命令支持**: 插件现已支持命令(command)功能，提供更丰富的交互方式
- **示例插件升级**: 更新禁言插件、豆包绘图插件、TTS插件等示例插件
- **配置文件管理**: 插件支持自动生成和管理配置文件，支持版本自动更新
- **文档完善**: 补全插件API文档，提供详细的开发指南

### 👥 人物印象系统
**麦麦现在能认得群友，记住每个人的特点**
- **人物侧写功能**: 加入了人物侧写！麦麦现在能认得群友，新增用户侧写功能，将印象拆分为多方面特点

### ⚡ Focus模式大幅优化 - 降低Token消耗与提升速度
- **Planner架构更新**: 更新planner架构，大大加快速度和表现效果！
- **处理器重构**: 
  - 移除冗余处理器
  - 精简处理器上下文，减少不必要的处理
  - 后置工具处理器，大大减少token消耗
- **统计系统**: 提供focus统计功能，可查看详细的no_action统计信息


### ⏰ 聊天频率精细控制
**支持时段化的精细频率管理，让麦麦在合适的时间说合适的话**
- **时段化控制**: 添加时段talk_frequency控制，支持不同时间段不同群聊的精细频率管理
- **严格频率控制**: 实现更加严格和可靠的频率控制机制
- **Normal模式优化**: 大幅优化normal模式的频率控制逻辑，提升回复的智能性

### 🎭 表达方式系统大幅优化
**智能学习群友聊天风格，让麦麦的表达更加多样化**
- **智能学习机制**: 优化表达方式学习算法，支持衰减机制，太久没学的会被自动抛弃
- **表达方式选择**: 新增表达方式选择器，让表达使用更合理
- **跨群互通配置**: 表达方式现在可以选择在不同群互通或独立
- **可视化工具**: 提供表达方式可视化脚本和检查脚本

### 💾 记忆系统改进
**更快的记忆处理和更好的短期记忆管理**
- **海马体优化**: 大大优化海马体同步速度，提升记忆处理效率
- **工作记忆升级**: 精简升级工作记忆模块，提供更好的短期记忆管理
- **聊天记录构建**: 优化聊天记录构建方式，提升记忆提取效率

### 📊 日志系统重构
**使用structlog提供更好的结构化日志**
- **structlog替换**: 使用structlog替代loguru，提供更好的结构化日志
- **日志查看器**: 新增日志查看脚本，支持更好的日志浏览
- **可配置日志**: 提供可配置的日志级别和格式，支持不同环境的需求

### 🎯 其他改进
- **emoji系统**: 移除emoji默认发送模式，优化表情包审查功能
- **控制台发送**: 添加不完善的控制台发送功能
- **行为准则**: 添加贡献者契约行为准则
- **图像清理**: 自动清理images文件夹，优化存储空间使用




## [0.7.0] -2025-6-1
- 你可以选择normal,focus和auto多种不同的聊天方式。normal提供更少的消耗，更快的回复速度。focus提供更好的聊天理解，更多工具使用和插件能力
- 现在，你可以自定义麦麦的表达方式，并且麦麦也可以学习群友的聊天风格（需要在配置文件中打开）
- 不再需要繁琐的安装MongoDB！弃用MongoDB，采用轻量sqlite,无需额外安装（提供数据迁移脚本）
- focus模式初步支持了插件，我们提供了两个示例插件（需要手动启用），可以让麦麦实现更丰富的操作。禁言插件和豆包绘图插件是示例用插件。

**重构专注聊天(HFC - focus_chat)**
- 模块化设计，可以自定义不同的部件
   - 观察器（获取信息）
   - 信息处理器（处理信息）
      - 重构：聊天思考（子心流）处理器
      - 重构：聊天处理器
      - 重构：聊天元信息处理器
      - 重构：工具处理器
      - 新增：工作记忆处理器
      - 新增：自我认知处理器
      - 新增：动作处理器
   - 决策器（选择动作）
   - 执行器（执行动作）
      - 回复动作
      - 不回复动作
      - 退出HFC动作
      - 插件：禁言动作
   - 表达器：装饰语言风格
- 可通过插件添加和自定义HFC部件（目前只支持action定义）
- 为专注模式添加关系线索
- 在专注模式下，麦麦可以决定自行发送语音消息（需要搭配tts适配器）
- 优化reply，减少复读
- 可自定义连续回复次数
- 可自定义处理器超时时间

**优化普通聊天(normal_chat)**
- 添加可学习的表达方式
- 增加了talk_frequency参数来有效控制回复频率
- 优化了进入和离开normal_chat的方式
- 添加时间信息

**新增表达方式学习**
- 麦麦配置单独表达方式
- 自主学习群聊中的表达方式，更贴近群友
- 可自定义的学习频率和开关
- 根据人设生成额外的表达方式

**聊天管理**
- 移除不在线状态
- 优化自动模式下normal与focus聊天的切换机制
- 大幅精简聊天状态切换规则，减少复杂度
- 移除聊天限额数量

**插件系统**
- 示例插件：禁言插件
- 示例插件：豆包绘图插件

**人格**
- 简化了人格身份的配置
- 优化了在focus模式下人格的表现和稳定性

**数据库重构**
- 移除了默认使用MongoDB，采用轻量sqlite
- 无需额外安装数据库
- 提供迁移脚本

**优化**
- 移除日程系统，减少幻觉（将会在未来版本回归）
- 移除主心流思考和LLM进入聊天判定
- 支持qwen3模型，支持自定义是否思考和思考长度
- 优化提及和at的判定
- 添加配置项
- 添加临时配置文件读取器


## [0.6.3-fix-4] - 2025-5-18
- 0.6.3 的最后一个修复版

### fix1-fix4修复日志
**聊天状态**
   - 大幅精简聊天状态切换，提高麦麦说话能力
   - 移除OFFLINE和ABSENT状态
   - 移除聊天数量限制
   - 聊天默认normal_chat
   - 默认关闭focus_chat

**知识库LPMM**
   - 增加嵌入模型一致性校验功能
   - 强化数据导入处理，增加非法文段检测功能
   - 修正知识获取逻辑，调整相关性输出顺序
   - 添加数据导入的用户确认删除功能

**专注模式**
   - 默认提取记忆，优化记忆表现
   - 添加心流查重
   - 为复读增加硬限制
   - 支持获取子心流循环信息和状态的API接口
   - 优化工具调用的信息获取与缓存

**表情包系统**
   - 优化表情包识别和处理
   - 提升表情匹配逻辑

**日志系统**
   - 优化日志样式配置
   - 添加丰富的追踪信息以增强调试能力

**API**
   - 添加GraphQL路由支持
   - 新增强制停止MAI Bot的API接口


## [0.6.3] - 2025-4-15

### 摘要
- MaiBot 0.6.3 版本发布！核心重构回复逻辑，统一为心流系统管理，智能切换交互模式。
- 引入全新的 LPMM 知识库系统，大幅提升信息获取能力。
- 新增昵称系统，改善群聊中的身份识别。
- 提供独立的桌宠适配器连接程序。
- 优化日志输出，修复若干问题。

### 🌟 核心功能增强
#### 统一回复逻辑 (Unified Reply Logic)
- **核心重构**: 移除了经典 (Reasoning) 与心流 (Heart Flow) 模式的区分，将回复逻辑完全整合到 `SubHeartflow` 中进行统一管理，由主心流统一调控。保留 Heart FC 模式的特色功能。
- **智能交互模式**: `SubHeartflow` 现在可以根据情境智能选择不同的交互模式：
    - **普通聊天 (Normal Chat)**: 类似于之前的 Reasoning 模式，进行常规回复（激活逻辑暂未改变）。
    - **心流聊天 (Heart Flow Chat)**: 基于改进的 PFC 模式，能更好地理解上下文，减少重复和认错人的情况，并支持**工具调用**以获取额外信息。
    - **离线模式 (Offline/Absent)**: 在特定情况下，麦麦可能会选择暂时不查看或回复群聊消息。
- **状态管理**: 交互模式的切换由 `SubHeartflow` 内部逻辑和 `SubHeartflowManager` 根据整体状态 (`MaiState`) 和配置进行管理。
- **流程优化**: 拆分了子心流的思考模块，使整体对话流程更加清晰。
- **状态判断改进**: 将 CHAT 状态判断交给 LLM 处理，使对话更自然。
- **回复机制**: 实现更为灵活的概率回复机制，使机器人能够自然地融入群聊环境。
- **重复性检查**: 加入心流回复重复性检查机制，防止麦麦陷入固定回复模式。

#### 全新知识库系统 (New Knowledge Base System - LPMM)
- **引入 LPMM**: 新增了 **LPMM (Large Psychology Model Maker)** 知识库系统，具有强大的信息检索能力，能显著提升麦麦获取和利用知识的效率。
- **功能集成**: 集成了 LPMM 知识库查询功能，进一步扩展信息检索能力。
- **推荐使用**: 强烈建议使用新的 LPMM 系统以获得最佳体验。旧的知识库系统仍然可用作为备选。

#### 昵称系统 (Nickname System)
- **自动取名**: 麦麦现在会尝试给群友取昵称，减少对易变的群昵称的依赖，从而降低认错人的概率。
- **持续完善**: 该系统目前仍处于早期阶段，会持续进行优化。

#### 记忆与上下文增强 (Memory and Context Enhancement)
- **聊天记录压缩**: 大幅优化聊天记录压缩系统，使机器人能够处理5倍于之前的上下文记忆量。
- **长消息截断**: 新增了长消息自动截断与模糊化功能，随着时间推移降低超长消息的权重，避免被特定冗余信息干扰。
- **记忆提取**: 优化记忆提取功能，提高对历史对话的理解和引用能力。
- **记忆整合**: 为记忆系统加入了合并与整合机制，优化长期记忆的结构与效率。
- **中期记忆调用**: 完善中期记忆调用机制，使机器人能够更自然地回忆和引用较早前的对话。
- **Prompt 优化**: 进一步优化了关系系统和记忆系统相关的提示词（prompt）。

#### 私聊 PFC 功能增强 (Private Chat PFC Enhancement)
- **功能修复与优化**: 修复了私聊 PFC 载入聊天记录缺失的 bug，优化了 prompt 构建，增加了审核机制，调整了重试次数，并将机器人发言存入数据库。
- **实验性质**: 请注意，PFC 仍然是一个实验性功能，可能在未来版本中被修改或移除，目前不接受相关 Bug 反馈。

#### 情感与互动增强 (Emotion and Interaction Enhancement)
- **全新表情包系统**: 新的表情包系统上线，表情含义更丰富，发送更快速。
- **表情包使用优化**: 优化了表情包的选择逻辑，减少重复使用特定表情包的情况，使表达更生动。
- **提示词优化**: 优化提示词（prompt）构建，增强对话质量和情感表达。
- **积极性配置**: 优化"让麦麦更愿意说话"的相关配置，使机器人更积极参与对话。
- **颜文字保护**: 保护颜文字处理机制，确保表情正确显示。

#### 工具与集成 (Tools and Integration)
- **动态更新**: 使用工具调用来更新关系和心情，取代原先的固定更新机制。
- **智能调用**: 工具调用时会考虑上下文，使调用更加智能。
- **知识库依赖**: 添加 LPMM 知识库依赖，扩展知识检索工具。

### 💻 系统架构优化
#### 日志优化 (Logging Optimization)
- **输出更清晰**: 优化了日志信息的格式和内容，使其更易于阅读和理解。

#### 模型与消息整合 (Model and Message Integration)
- **模型合并**: 合并工具调用模型和心流模型，提高整体一致性。
- **消息规范**: 全面改用 `maim_message`，移除对 `rest` 的支持。

#### (临时) 简易 GUI (Temporary Simple GUI)
- **运行状态查看**: 提供了一个非常基础的图形用户界面，用于查看麦麦的运行状态。
- **临时方案**: 这是一个临时性的解决方案，功能简陋，**将在 0.6.4 版本中被全新的 Web UI 所取代**。此 GUI 不会包含在主程序包中，而是通过一键包提供，并且不接受 Bug 反馈。

### 🐛 问题修复
- **记忆检索优化**: 提高了记忆检索的准确性和效率。
- 修复了一些其他小问题。

### 🔧 其他改进
#### 桌宠适配器 (Bug Catcher Adapter)
- **独立适配器**: 提供了一个"桌宠"独立适配器，用于连接麦麦和桌宠。
- **获取方式**: 可在 MaiBot 的 GitHub 组织中找到该适配器，不包含在主程序内。

#### 一键包内容 (One-Click Package Contents)
- **辅助程序**: 一键包中包含了简易 GUI 和 **麦麦帮助配置** 等辅助程序，后者可在配置出现问题时提供帮助。

## [0.6.2] - 2025-4-14

### 摘要
- MaiBot 0.6.2 版本发布！
- 优化了心流的观察系统，优化提示词和表现，现在心流表现更好！
- 新增工具调用能力，可以更好地获取信息
- 本次更新主要围绕工具系统、心流系统、消息处理和代码优化展开，新增多个工具类，优化了心流系统的逻辑，改进了消息处理流程，并修复了多个问题。

### 🌟 核心功能增强
#### 工具系统
- 新增了知识获取工具系统，支持通过心流调用获取多种知识
- 新增了工具系统使用指南，详细说明工具结构、自动注册机制和添加步骤
- 新增了多个实用工具类，包括心情调整工具`ChangeMoodTool`、关系查询工具`RelationshipTool`、数值比较工具`CompareNumbersTool`、日程获取工具`GetCurrentTaskTool`、上下文压缩工具`CompressContextTool`和知识获取工具`GetKnowledgeTool`
- 更新了`ToolUser`类，支持自动获取已注册工具定义并调用`execute`方法
- 需要配置支持工具调用的模型才能使用完整功能

#### 心流系统
- 新增了上下文压缩缓存功能，可以有更持久的记忆
- 新增了心流系统的README.md文件，详细介绍了系统架构、主要功能和工作流程。
- 优化了心流系统的逻辑，包括子心流自动清理和合理配置更新间隔。
- 改进了心流观察系统，优化了提示词设计和系统表现，使心流运行更加稳定高效。
- 更新了`Heartflow`类的方法和属性，支持异步生成提示词并提升生成质量。

#### 消息处理
- 改进了消息处理流程，包括回复检查、消息生成和发送逻辑。
- 新增了`ReplyGenerator`类，用于根据观察信息和对话信息生成回复。
- 优化了消息队列管理系统，支持按时间顺序处理消息。

#### 现在可以启用更好的表情包发送系统

### 💻 系统架构优化

#### 部署支持
- 更新了Docker部署文档，优化了服务配置和挂载路径。
- 完善了Linux和Windows脚本支持。

### 🐛 问题修复
- 修复了消息处理器中的正则表达式匹配问题。
- 修复了图像处理中的帧大小和拼接问题。
- 修复了私聊时产生`reply`消息的bug。
- 修复了配置文件加载时的版本兼容性问题。

### 📚 文档更新
- 更新了`README.md`文件，包括Python版本要求和协议信息。
- 新增了工具系统和心流系统的详细文档。
- 优化了部署相关文档的完整性。

### 🔧 其他改进
- 新增了崩溃日志记录器，记录崩溃信息到日志文件。
- 优化了统计信息输出，在控制台显示详细统计信息。
- 改进了异常处理机制，提升系统稳定性。
- 现可配置部分模型的temp参数

## [0.6.0] - 2025-4-4

### 摘要
- MaiBot 0.6.0 重磅升级！ 核心重构为独立智能体MaiCore，新增思维流对话系统，支持拟真思考过程。记忆与关系系统2.0让交互更自然，动态日程引擎实现智能调整。优化部署流程，修复30+稳定性问题，隐私政策同步更新，推荐所有用户升级体验全新AI交互！（V3激烈生成）

### 🌟 核心功能增强
#### 架构重构
- 将MaiBot重构为MaiCore独立智能体
- 移除NoneBot相关代码，改为插件方式与NoneBot对接

#### 思维流系统
- 提供两种聊天逻辑，思维流（心流）聊天（ThinkFlowChat）和推理聊天（ReasoningChat）
- 思维流聊天能够在回复前后进行思考
- 思维流自动启停机制，提升资源利用效率
- 思维流与日程系统联动，实现动态日程生成

#### 回复系统
- 更改了回复引用的逻辑，从基于时间改为基于新消息
- 提供私聊的PFC模式，可以进行有目的，自由多轮对话（实验性）

#### 记忆系统优化
- 优化记忆抽取策略
- 优化记忆prompt结构
- 改进海马体记忆提取机制，提升自然度

#### 关系系统优化
- 优化关系管理系统，适用于新版本
- 改进关系值计算方式，提供更丰富的关系接口

#### 表情包系统
- 可以识别gif表情包
- 表情包增加存储上限
- 自动清理缓存图片

## 日程系统优化
- 日程现在动态更新
- 日程可以自定义想象力程度
- 日程会与聊天情况交互（思维流模式下）

### 💻 系统架构优化
#### 配置系统改进
- 新增更多项目的配置项
- 修复配置文件保存问题
- 优化配置结构：
  - 调整模型配置组织结构
  - 优化配置项默认值
  - 调整配置项顺序
- 移除冗余配置

#### 部署支持扩展
- 优化Docker构建流程
- 完善Windows脚本支持
- 优化Linux一键安装脚本

### 🐛 问题修复
#### 功能稳定性
- 修复表情包审查器问题
- 修复心跳发送问题
- 修复拍一拍消息处理异常
- 修复日程报错问题
- 修复文件读写编码问题
- 修复西文字符分割问题
- 修复自定义API提供商识别问题
- 修复人格设置保存问题
- 修复EULA和隐私政策编码问题

### 📚 文档更新
- 更新README.md内容
- 优化文档结构
- 更新EULA和隐私政策
- 完善部署文档

### 🔧 其他改进
- 新增详细统计系统
- 优化表情包审查功能
- 改进消息转发处理
- 优化代码风格和格式
- 完善异常处理机制
- 可以自定义时区
- 优化日志输出格式
- 版本硬编码，新增配置自动更新功能
- 优化了统计信息，会在控制台显示统计信息


## [0.5.15] - 2025-3-17
### 🌟 核心功能增强
#### 关系系统升级
- 新增关系系统构建与启用功能
- 优化关系管理系统
- 改进prompt构建器结构
- 新增手动修改记忆库的脚本功能
- 增加alter支持功能

#### 启动器优化
- 新增MaiLauncher.bat 1.0版本
- 优化Python和Git环境检测逻辑
- 添加虚拟环境检查功能
- 改进工具箱菜单选项
- 新增分支重置功能
- 添加MongoDB支持
- 优化脚本逻辑
- 修复虚拟环境选项闪退和conda激活问题
- 修复环境检测菜单闪退问题
- 修复.env文件复制路径错误

#### 日志系统改进
- 新增GUI日志查看器
- 重构日志工厂处理机制
- 优化日志级别配置
- 支持环境变量配置日志级别
- 改进控制台日志输出
- 优化logger输出格式

### 💻 系统架构优化
#### 配置系统升级
- 更新配置文件到0.0.10版本
- 优化配置文件可视化编辑
- 新增配置文件版本检测功能
- 改进配置文件保存机制
- 修复重复保存可能清空list内容的bug
- 修复人格设置和其他项配置保存问题

#### WebUI改进
- 优化WebUI界面和功能
- 支持安装后管理功能
- 修复部分文字表述错误

#### 部署支持扩展
- 优化Docker构建流程
- 改进MongoDB服务启动逻辑
- 完善Windows脚本支持
- 优化Linux一键安装脚本
- 新增Debian 12专用运行脚本

### 🐛 问题修复
#### 功能稳定性
- 修复bot无法识别at对象和reply对象的问题
- 修复每次从数据库读取额外加0.5的问题
- 修复新版本由于版本判断不能启动的问题
- 修复配置文件更新和学习知识库的确认逻辑
- 优化token统计功能
- 修复EULA和隐私政策处理时的编码兼容问题
- 修复文件读写编码问题，统一使用UTF-8
- 修复颜文字分割问题
- 修复willing模块cfg变量引用问题

### 📚 文档更新
- 更新CLAUDE.md为高信息密度项目文档
- 添加mermaid系统架构图和模块依赖图
- 添加核心文件索引和类功能表格
- 添加消息处理流程图
- 优化文档结构
- 更新EULA和隐私政策文档

### 🔧 其他改进
- 更新全球在线数量展示功能
- 优化statistics输出展示
- 新增手动修改内存脚本（支持添加、删除和查询节点和边）

### 主要改进方向
1. 完善关系系统功能
2. 优化启动器和部署流程
3. 改进日志系统
4. 提升配置系统稳定性
5. 加强文档完整性

## [0.5.14] - 2025-3-14
### 🌟 核心功能增强
#### 记忆系统优化
- 修复了构建记忆时重复读取同一段消息导致token消耗暴增的问题
- 优化了记忆相关的工具模型代码

#### 消息处理升级
- 新增了不回答已撤回消息的功能
- 新增每小时自动删除存留超过1小时的撤回消息
- 优化了戳一戳功能的响应机制
- 修复了回复消息未正常发送的问题
- 改进了图片发送错误时的处理机制

#### 日程系统改进
- 修复了长时间运行的bot在跨天后无法生成新日程的问题
- 优化了日程文本解析功能
- 修复了解析日程时遇到markdown代码块等额外内容的处理问题

### 💻 系统架构优化
#### 日志系统升级
- 建立了新的日志系统
- 改进了错误处理机制
- 优化了代码格式化规范

#### 部署支持扩展
- 改进了NAS部署指南，增加HOST设置说明
- 优化了部署文档的完整性

### 🐛 问题修复
#### 功能稳定性
- 修复了utils_model.py中的潜在问题
- 修复了set_reply相关bug
- 修复了回应所有戳一戳的问题
- 优化了bot被戳时的判断逻辑

### 📚 文档更新
- 更新了README.md的内容
- 完善了NAS部署指南
- 优化了部署相关文档

### 主要改进方向
1. 提升记忆系统的效率和稳定性
2. 完善消息处理机制
3. 优化日程系统功能
4. 改进日志和错误处理
5. 加强部署文档的完整性

## [0.5.13] - 2025-3-12
### 🌟 核心功能增强
#### 记忆系统升级
- 新增了记忆系统的时间戳功能，包括创建时间和最后修改时间
- 新增了记忆图节点和边的时间追踪功能
- 新增了自动补充缺失时间字段的功能
- 新增了记忆遗忘机制，基于时间条件自动遗忘旧记忆
- 优化了记忆系统的数据同步机制
- 优化了记忆系统的数据结构，确保所有数据类型的一致性

#### 私聊功能完善
- 新增了完整的私聊功能支持，包括消息处理和回复
- 新增了聊天流管理器，支持群聊和私聊的上下文管理
- 新增了私聊过滤开关功能
- 优化了关系管理系统，支持跨平台用户关系

#### 消息处理升级
- 新增了消息队列管理系统，支持按时间顺序处理消息
- 新增了消息发送控制器，实现人性化的发送速度和间隔
- 新增了JSON格式分享卡片读取支持
- 新增了Base64格式表情包CQ码支持
- 改进了消息处理流程，支持多种消息类型

### 💻 系统架构优化
#### 配置系统改进
- 新增了配置文件自动更新和版本检测功能
- 新增了配置文件热重载API接口
- 新增了配置文件版本兼容性检查
- 新增了根据不同环境(dev/prod)显示不同级别的日志功能
- 优化了配置文件格式和结构

#### 部署支持扩展
- 新增了Linux系统部署指南
- 新增了Docker部署支持的详细文档
- 新增了NixOS环境支持（使用venv方式）
- 新增了优雅的shutdown机制
- 优化了Docker部署文档

### 🛠️ 开发体验提升
#### 工具链升级
- 新增了ruff代码格式化和检查工具
- 新增了知识库一键启动脚本
- 新增了自动保存脚本，定期保存聊天记录和关系数据
- 新增了表情包自动获取脚本
- 优化了日志记录（使用logger.debug替代print）
- 精简了日志输出，禁用了Uvicorn/NoneBot默认日志

#### 安全性强化
- 新增了API密钥安全管理机制
- 新增了数据库完整性检查功能
- 新增了表情包文件完整性自动检查
- 新增了异常处理和自动恢复机制
- 优化了安全性检查机制

### 🐛 关键问题修复
#### 系统稳定性
- 修复了systemctl强制停止的问题
- 修复了ENVIRONMENT变量在同一终端下不能被覆盖的问题
- 修复了libc++.so依赖问题
- 修复了数据库索引创建失败的问题
- 修复了MongoDB连接配置相关问题
- 修复了消息队列溢出问题
- 修复了配置文件加载时的版本兼容性问题

#### 功能完善性
- 修复了私聊时产生reply消息的bug
- 修复了回复消息无法识别的问题
- 修复了CQ码解析错误
- 修复了情绪管理器导入问题
- 修复了小名无效的问题
- 修复了表情包发送时的参数缺失问题
- 修复了表情包重复注册问题
- 修复了变量拼写错误问题

### 主要改进方向
1. 提升记忆系统的智能性和可靠性
2. 完善私聊功能的完整生态
3. 优化系统架构和部署便利性
4. 提升开发体验和代码质量
5. 加强系统安全性和稳定性



