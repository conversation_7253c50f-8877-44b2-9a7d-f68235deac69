import time
import asyncio
import hashlib
import json
import re
import aiohttp
from datetime import datetime, timedelta
from typing import List, Tuple, Type, Dict, Any, Optional
# 使用Google Generative AI SDK进行联网搜索
import google.generativeai as genai
from tenacity import retry, stop_after_attempt, wait_exponential  # 重试机制
from src.common.logger import get_logger
from src.plugin_system import (
    BasePlugin,
    register_plugin,
    BaseTool,
    ComponentInfo,
    ConfigField,
    ToolParamType,
    message_api
)
from src.plugin_system.apis import send_api
from src.chat.message_receive.chat_stream import get_chat_manager

logger = get_logger("internet_search_plugin")


class AdvancedSearchCache:
    """高级搜索结果缓存管理器 - 支持2M数据量和3分钟过期"""

    def __init__(self, max_size_mb: float = 2.0, expire_minutes: int = 3, max_entries: int = 1000):
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.max_size_bytes = int(max_size_mb * 1024 * 1024)  # 转换为字节
        self.expire_minutes = expire_minutes
        self.max_entries = max_entries
        self.current_size_bytes = 0

        # 预搜索缓存
        self.presearch_cache: Dict[str, Dict[str, Any]] = {}

        # 启动定期清理任务
        asyncio.create_task(self._periodic_cleanup())

    def _generate_key(self, query: str) -> str:
        """生成缓存键"""
        return hashlib.md5(query.lower().strip().encode()).hexdigest()

    def _calculate_size(self, data: Any) -> int:
        """计算数据大小（字节）"""
        return len(json.dumps(data, ensure_ascii=False).encode('utf-8'))

    def _is_expired(self, timestamp: datetime) -> bool:
        """检查缓存是否过期（3分钟）"""
        return datetime.now() - timestamp > timedelta(minutes=self.expire_minutes)

    def get(self, query: str) -> Optional[Dict[str, Any]]:
        """获取缓存结果"""
        key = self._generate_key(query)
        if key in self.cache:
            cache_data = self.cache[key]
            if not self._is_expired(cache_data['timestamp']):
                # 更新访问时间
                cache_data['last_access'] = datetime.now()
                return cache_data['result']
            else:
                # 删除过期缓存
                self._remove_cache_entry(key)
        return None

    def set(self, query: str, result: Dict[str, Any]):
        """设置缓存结果"""
        key = self._generate_key(query)
        data_size = self._calculate_size(result)

        # 检查数据大小是否超过限制
        if data_size > self.max_size_bytes:
            logger.warning(f"缓存数据过大，跳过缓存: {data_size} bytes")
            return

        # 清理空间
        self._ensure_space(data_size)

        # 如果条目数量超过限制，删除最旧的条目
        if len(self.cache) >= self.max_entries:
            self._remove_oldest_entry()

        # 添加新缓存
        cache_entry = {
            'result': result,
            'timestamp': datetime.now(),
            'last_access': datetime.now(),
            'size': data_size
        }

        self.cache[key] = cache_entry
        self.current_size_bytes += data_size

        logger.debug(f"缓存已更新: {query[:50]}..., 大小: {data_size} bytes, 总大小: {self.current_size_bytes} bytes")

    def _remove_cache_entry(self, key: str):
        """删除缓存条目"""
        if key in self.cache:
            self.current_size_bytes -= self.cache[key]['size']
            del self.cache[key]

    def _remove_oldest_entry(self):
        """删除最旧的缓存条目"""
        if not self.cache:
            return

        oldest_key = min(self.cache.keys(),
                        key=lambda k: self.cache[k]['last_access'])
        self._remove_cache_entry(oldest_key)

    def _ensure_space(self, needed_size: int):
        """确保有足够的缓存空间"""
        while (self.current_size_bytes + needed_size > self.max_size_bytes and
               len(self.cache) > 0):
            self._remove_oldest_entry()

    async def _periodic_cleanup(self):
        """定期清理过期缓存"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟清理一次
                self.clear_expired()
            except Exception as e:
                logger.error(f"定期清理缓存失败: {e}")

    def clear_expired(self):
        """清理过期缓存"""
        expired_keys = [
            key for key, data in self.cache.items()
            if self._is_expired(data['timestamp'])
        ]
        for key in expired_keys:
            self._remove_cache_entry(key)

        if expired_keys:
            logger.info(f"清理了 {len(expired_keys)} 个过期缓存条目")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "total_entries": len(self.cache),
            "total_size_mb": round(self.current_size_bytes / (1024 * 1024), 2),
            "max_size_mb": self.max_size_bytes / (1024 * 1024),
            "usage_percent": round((self.current_size_bytes / self.max_size_bytes) * 100, 1)
        }


class SearchResultProcessor:
    """搜索结果处理器 - 去重和合并"""

    def __init__(self):
        self.similarity_threshold = 0.8  # 相似度阈值

    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似度"""
        # 简单的相似度计算（基于词汇重叠）
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

    def deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果"""
        if not results:
            return results

        unique_results = []

        for result in results:
            is_duplicate = False
            result_text = result.get('content', '')

            for unique_result in unique_results:
                unique_text = unique_result.get('content', '')
                similarity = self._calculate_similarity(result_text, unique_text)

                if similarity > self.similarity_threshold:
                    is_duplicate = True
                    # 合并信息（保留更详细的结果）
                    if len(result_text) > len(unique_text):
                        unique_result.update(result)
                    break

            if not is_duplicate:
                unique_results.append(result)

        logger.info(f"去重完成: {len(results)} -> {len(unique_results)} 个结果")
        return unique_results

    def merge_results(self, quick_result: str, deep_results: List[Dict[str, Any]]) -> str:
        """合并快速分析和深度搜索结果"""
        if not deep_results:
            return quick_result

        # 去重深度搜索结果
        unique_deep_results = self.deduplicate_results(deep_results)

        # 构建合并后的结果
        merged_content = []

        # 添加快速分析结果
        if quick_result:
            merged_content.append(f"🔍 快速分析：\n{quick_result}")

        # 添加深度搜索结果
        if unique_deep_results:
            merged_content.append("\n📊 深度搜索结果：")
            for i, result in enumerate(unique_deep_results[:3], 1):  # 最多显示3个结果
                content = result.get('content', '')
                if content:
                    merged_content.append(f"\n{i}. {content}")

        return "\n".join(merged_content)


class DynamicWeightAnalyzer:
    """动态权重分析器 - 基于关键词的智能权重系统"""

    def __init__(self):
        # 关键词权重映射表
        self.keyword_weights = {
            # 高权重关键词 (需要深度分析)
            "high": {
                "keywords": ["分析", "比较", "评价", "原理", "机制", "影响", "趋势", "预测", "为什么", "如何"],
                "weight": 0.8,
                "description": "需要深度分析的复杂查询"
            },
            # 中权重关键词 (可能需要补充)
            "medium": {
                "keywords": ["详细", "具体", "全面", "深入", "背景", "历史", "发展", "现状"],
                "weight": 0.5,
                "description": "可能需要补充信息的查询"
            },
            # 低权重关键词 (简单事实查询)
            "low": {
                "keywords": ["什么时候", "多少钱", "价格", "时间", "日期", "是什么", "在哪"],
                "weight": 0.2,
                "description": "简单事实查询"
            }
        }

        # 专业领域权重
        self.domain_weights = {
            "technology": {"keywords": ["AI", "人工智能", "算法", "编程", "软件", "硬件"], "weight": 0.3},
            "finance": {"keywords": ["股票", "投资", "金融", "经济", "市场", "货币"], "weight": 0.4},
            "gaming": {"keywords": ["游戏", "电竞", "主机", "PC", "手游", "网游"], "weight": 0.2},
            "entertainment": {"keywords": ["电影", "音乐", "明星", "娱乐", "综艺", "电视剧"], "weight": 0.1}
        }

        # 查询意图权重
        self.intent_weights = {
            "fact_query": {"patterns": [r"是什么", r"什么时候", r"多少", r"在哪"], "weight": 0.1},
            "comparison": {"patterns": [r"比较", r"对比", r"哪个好", r"区别"], "weight": 0.7},
            "analysis": {"patterns": [r"分析", r"评价", r"怎么样", r"如何"], "weight": 0.8},
            "prediction": {"patterns": [r"预测", r"趋势", r"未来", r"发展"], "weight": 0.9}
        }

    def analyze_query_complexity(self, query: str, quick_result: str = "") -> dict:
        """基于用户真实聊天话题的智能权重分析 - 增强版"""
        try:
            # 1. 分析用户真实意图和话题
            user_intent = self._analyze_user_real_intent(query)

            # 2. 评估话题的深度需求
            topic_depth = self._evaluate_topic_depth_need(query, user_intent)

            # 3. 检查快速结果的话题匹配度
            result_relevance = self._check_result_topic_relevance(quick_result, user_intent)

            # 4. 【新增】智能知识盲区检测
            knowledge_gaps = self._detect_knowledge_gaps(query)

            # 5. 【新增】计算聊天增强权重
            chat_enhancement_weight = self._calculate_chat_enhancement_weight(query, knowledge_gaps, user_intent)

            # 6. 基于真实聊天需求计算权重（整合新权重）
            chat_weight = self._calculate_chat_based_weight(user_intent, topic_depth, result_relevance)
            final_weight = max(chat_weight, chat_enhancement_weight)  # 取较高权重

            # 7. 防幻觉检查 - 确保不编造信息
            anti_hallucination_check = self._check_for_potential_hallucination(query, quick_result)

            # 8. 综合决策（使用最终权重）
            needs_deep_search = self._make_intelligent_decision(
                final_weight, anti_hallucination_check, user_intent, quick_result
            )

            return {
                "query": query,
                "user_intent": user_intent,
                "topic_depth": topic_depth,
                "result_relevance": result_relevance,
                "knowledge_gaps": knowledge_gaps,  # 新增
                "chat_weight": chat_weight,
                "chat_enhancement_weight": chat_enhancement_weight,  # 新增
                "final_weight": final_weight,  # 新增
                "anti_hallucination": anti_hallucination_check,
                "needs_deep_search": needs_deep_search,
                "reason": self._get_enhanced_decision_reason(user_intent, final_weight, knowledge_gaps, anti_hallucination_check)
            }

        except Exception as e:
            logger.error(f"智能权重分析失败: {e}")
            return self._fallback_intelligent_analysis(query)

    def _calculate_base_complexity(self, query: str) -> float:
        """基于数学算法的动态复杂度评分"""
        try:
            # 基础分词和预处理
            words = self._tokenize_query(query)
            if not words:
                return 0.2

            # 1. 词汇稀有度评分 (0-0.4)
            rarity_score = self._calculate_word_rarity(words)

            # 2. 语义复杂度评分 (0-0.3)
            semantic_score = self._calculate_semantic_complexity(words)

            # 3. 查询结构复杂度 (0-0.2)
            structure_score = self._calculate_structure_complexity(query, words)

            # 4. 上下文相关性评分 (0-0.1)
            context_score = self._calculate_context_relevance(words)

            # 综合评分
            total_score = rarity_score + semantic_score + structure_score + context_score

            # 归一化到 [0, 1] 区间
            normalized_score = min(1.0, max(0.0, total_score))

            logger.debug(f"复杂度计算: 稀有度={rarity_score:.3f}, 语义={semantic_score:.3f}, 结构={structure_score:.3f}, 上下文={context_score:.3f}, 总分={normalized_score:.3f}")

            return normalized_score

        except Exception as e:
            logger.error(f"复杂度计算失败: {e}")
            return 0.5  # 默认中等复杂度

    def _tokenize_query(self, query: str) -> list:
        """查询分词"""
        # 移除标点符号，保留中英文字符
        clean_query = re.sub(r'[^\u4e00-\u9fff\w\s]', ' ', query)
        # 分词
        words = [word.strip().lower() for word in clean_query.split() if len(word.strip()) > 1]
        return words

    def _calculate_word_rarity(self, words: list) -> float:
        """计算词汇稀有度评分"""
        if not words:
            return 0.0

        # 常见词汇列表（权重较低）
        common_words = {
            '什么', '怎么', '哪个', '多少', '时间', '价格', '是', '的', '了', '在',
            'what', 'how', 'when', 'where', 'which', 'is', 'are', 'the', 'a', 'an'
        }

        rarity_sum = 0.0
        for word in words:
            if word in common_words:
                rarity_sum += 0.1  # 常见词低权重
            elif len(word) <= 2:
                rarity_sum += 0.2  # 短词中等权重
            elif len(word) >= 6:
                rarity_sum += 0.6  # 长词高权重（可能是专业术语）
            else:
                rarity_sum += 0.4  # 中等长度词

        # 平均稀有度，最大0.4
        avg_rarity = min(0.4, rarity_sum / len(words))
        return avg_rarity

    def _calculate_semantic_complexity(self, words: list) -> float:
        """计算语义复杂度评分"""
        if not words:
            return 0.0

        complexity_indicators = {
            # 分析类词汇
            'analysis': ['分析', '评价', '比较', '对比', '研究', 'analyze', 'compare', 'evaluate'],
            # 技术类词汇
            'technical': ['算法', '原理', '机制', '技术', '系统', 'algorithm', 'principle', 'mechanism'],
            # 预测类词汇
            'predictive': ['预测', '趋势', '未来', '发展', 'predict', 'trend', 'future', 'development'],
            # 因果类词汇
            'causal': ['为什么', '原因', '导致', '影响', 'why', 'cause', 'effect', 'impact']
        }

        complexity_score = 0.0
        for category, keywords in complexity_indicators.items():
            for word in words:
                if any(keyword in word for keyword in keywords):
                    if category == 'analysis':
                        complexity_score += 0.08
                    elif category == 'technical':
                        complexity_score += 0.06
                    elif category == 'predictive':
                        complexity_score += 0.07
                    elif category == 'causal':
                        complexity_score += 0.05

        return min(0.3, complexity_score)

    def _calculate_structure_complexity(self, query: str, words: list) -> float:
        """计算查询结构复杂度"""
        structure_score = 0.0

        # 查询长度因子
        length_factor = min(0.1, len(query) / 100)
        structure_score += length_factor

        # 词汇数量因子
        word_count_factor = min(0.05, len(words) / 20)
        structure_score += word_count_factor

        # 复合句检测
        if '，' in query or '；' in query or '和' in query or 'and' in query:
            structure_score += 0.03

        # 疑问词数量
        question_words = ['什么', '怎么', '为什么', '哪个', 'what', 'how', 'why', 'which']
        question_count = sum(1 for word in words if word in question_words)
        if question_count > 1:
            structure_score += 0.02

        return min(0.2, structure_score)

    def _calculate_context_relevance(self, words: list) -> float:
        """计算上下文相关性评分"""
        # 专业领域词汇检测
        domain_keywords = {
            'tech': ['AI', '人工智能', '算法', '编程', '软件', '硬件', '数据'],
            'finance': ['股票', '投资', '金融', '经济', '市场', '货币', '价格'],
            'science': ['研究', '实验', '理论', '科学', '技术', '发现', '创新'],
            'business': ['公司', '企业', '商业', '管理', '战略', '市场', '竞争']
        }

        relevance_score = 0.0
        for domain, keywords in domain_keywords.items():
            domain_match = sum(1 for word in words if any(kw in word for kw in keywords))
            if domain_match > 0:
                relevance_score += min(0.025, domain_match * 0.01)

        return min(0.1, relevance_score)

    def _detect_knowledge_gaps(self, query: str) -> dict:
        """智能知识盲区检测 - 新增功能1（优化敏感度）"""
        gaps = {
            "has_gaps": False,
            "gap_types": [],
            "unknown_concepts": [],
            "technical_terms": [],
            "new_information": [],
            "gap_score": 0.0
        }

        query_lower = query.lower()

        # 1. 检测明确的知识询问（最高优先级）
        explicit_knowledge_patterns = [
            "是啥", "是什么", "什么意思", "怎么回事", "咋回事",
            "不懂", "不知道", "不清楚", "没听过", "怎么样",
            "好不好", "如何", "咋样", "介绍一下"
        ]

        for pattern in explicit_knowledge_patterns:
            if pattern in query_lower:
                gaps["gap_types"].append("explicit_knowledge_request")
                gaps["gap_score"] += 0.6  # 提高权重
                logger.debug(f"检测到明确知识询问: {pattern}")

        # 2. 检测新概念和专业术语
        import re
        technical_indicators = [
            r'\b[A-Z]{2,}\b',  # 大写缩写
            r'\b\w+[0-9]+\w*\b',  # 包含数字的术语
            r'\b[a-z]+[-_][a-z]+\b',  # 连字符术语
        ]

        for pattern in technical_indicators:
            matches = re.findall(pattern, query)
            if matches:
                gaps["technical_terms"].extend(matches)
                gaps["gap_score"] += 0.3
                logger.debug(f"检测到技术术语: {matches}")

        # 3. 检测游戏相关新术语（降低门槛）
        gaming_unknown_patterns = [
            r'(\w+号)',  # XX号
            r'(\w+探员)',  # XX探员
            r'(\w+地图)',  # XX地图
            r'(\w+模式)',  # XX模式
            r'(\w+技能)',  # XX技能
            r'(\w+游戏)',  # XX游戏
            r'(\w+版本)',  # XX版本
        ]

        for pattern in gaming_unknown_patterns:
            matches = re.findall(pattern, query)
            if matches:
                gaps["unknown_concepts"].extend(matches)
                gaps["gap_score"] += 0.4
                logger.debug(f"检测到游戏概念: {matches}")

        # 4. 检测时间敏感信息
        time_sensitive_keywords = [
            "最新", "新版本", "更新", "发布", "上线", "下线",
            "今天", "昨天", "最近", "刚刚", "现在", "新出的"
        ]

        for keyword in time_sensitive_keywords:
            if keyword in query_lower:
                gaps["new_information"].append(keyword)
                gaps["gap_score"] += 0.3
                logger.debug(f"检测到时效信息: {keyword}")

        # 5. 检测疑问语气（新增）
        question_indicators = [
            "吗", "呢", "？", "?", "嘛", "咋", "啥时候", "多少"
        ]

        for indicator in question_indicators:
            if indicator in query_lower:
                gaps["gap_types"].append("question_tone")
                gaps["gap_score"] += 0.2
                logger.debug(f"检测到疑问语气: {indicator}")
                break  # 只计算一次

        # 6. 综合评估（降低门槛）
        if gaps["gap_score"] > 0.2:  # 从0.3降到0.2
            gaps["has_gaps"] = True
            logger.info(f"🧠 知识盲区检测：发现盲区 - 得分: {gaps['gap_score']:.2f}, 类型: {gaps['gap_types']}")

        return gaps

    def _calculate_chat_enhancement_weight(self, query: str, knowledge_gaps: dict, user_intent: dict) -> float:
        """计算聊天增强权重 - 新增功能1"""
        base_weight = 0.2

        # 知识盲区权重
        if knowledge_gaps["has_gaps"]:
            base_weight += knowledge_gaps["gap_score"]

        # 用户明确询问权重
        if user_intent.get("specific_info_needed", False):
            base_weight += 0.3

        # 技术术语权重
        if len(knowledge_gaps["technical_terms"]) > 0:
            base_weight += 0.2

        # 新概念权重
        if len(knowledge_gaps["unknown_concepts"]) > 0:
            base_weight += 0.3

        return min(1.0, base_weight)

    def _analyze_user_real_intent(self, query: str) -> dict:
        """分析用户真实聊天意图"""
        intent = {
            "type": "unknown",
            "topic": "",
            "casual_level": 0.5,
            "specific_info_needed": False
        }

        query_lower = query.lower()

        # 游戏相关聊天
        if any(game in query_lower for game in ["三角洲", "糖人号", "唐人号", "探员", "游戏"]):
            intent["type"] = "gaming_chat"
            intent["topic"] = "游戏讨论"
            intent["casual_level"] = 0.8  # 很随意的聊天
            intent["specific_info_needed"] = "号" in query or "是啥" in query

        # 简单事实查询
        elif any(word in query_lower for word in ["什么时候", "多少钱", "价格", "时间"]):
            intent["type"] = "fact_query"
            intent["topic"] = "事实查询"
            intent["casual_level"] = 0.3
            intent["specific_info_needed"] = True

        # 随意聊天
        elif any(word in query_lower for word in ["是啥", "咋样", "怎么样"]):
            intent["type"] = "casual_chat"
            intent["topic"] = "随意聊天"
            intent["casual_level"] = 0.9
            intent["specific_info_needed"] = False

        return intent

    def _evaluate_topic_depth_need(self, query: str, user_intent: dict) -> float:
        """评估话题需要的深度"""
        if user_intent["type"] == "casual_chat":
            return 0.2  # 随意聊天不需要太深
        elif user_intent["type"] == "gaming_chat":
            return 0.3  # 游戏聊天稍微深一点
        elif user_intent["type"] == "fact_query":
            return 0.6  # 事实查询需要准确信息
        else:
            return 0.4  # 默认中等深度

    def _check_result_topic_relevance(self, result: str, user_intent: dict) -> float:
        """检查结果与用户真实话题的相关性"""
        if not result:
            return 0.0

        result_lower = result.lower()

        # 如果是游戏聊天，检查是否有游戏相关内容
        if user_intent["type"] == "gaming_chat":
            game_keywords = ["游戏", "玩家", "角色", "装备", "技能", "地图"]
            relevance = sum(1 for keyword in game_keywords if keyword in result_lower)
            return min(1.0, relevance * 0.2)

        # 其他类型的相关性检查
        return 0.5  # 默认中等相关性

    def _calculate_chat_based_weight(self, user_intent: dict, topic_depth: float, result_relevance: float) -> float:
        """基于真实聊天需求计算权重"""
        base_weight = 0.3

        # 随意聊天降低权重
        if user_intent["casual_level"] > 0.7:
            base_weight *= 0.5

        # 如果结果相关性低，增加权重
        if result_relevance < 0.3:
            base_weight += 0.3

        # 如果需要具体信息但没有得到，增加权重
        if user_intent["specific_info_needed"] and result_relevance < 0.5:
            base_weight += 0.4

        return min(1.0, base_weight)

    def _check_for_potential_hallucination(self, query: str, result: str) -> dict:
        """检查潜在的幻觉风险"""
        check = {
            "risk_level": "low",
            "risk_factors": [],
            "should_avoid_deep_search": False
        }

        if not result:
            check["risk_level"] = "high"
            check["risk_factors"].append("无搜索结果，容易编造")
            check["should_avoid_deep_search"] = True
            return check

        # 检查是否包含明显的编造内容指标
        hallucination_indicators = [
            "可能指", "据说", "传言", "可能是", "或许", "大概", "应该是"
        ]

        for indicator in hallucination_indicators:
            if indicator in result:
                check["risk_factors"].append(f"包含不确定表达: {indicator}")

        if len(check["risk_factors"]) > 2:
            check["risk_level"] = "high"
            check["should_avoid_deep_search"] = True
        elif len(check["risk_factors"]) > 0:
            check["risk_level"] = "medium"

        return check

    def _make_intelligent_decision(self, chat_weight: float, anti_hallucination: dict, user_intent: dict, result: str) -> bool:
        """基于智能分析做出决策 - 增强知识盲区检测"""
        # 如果幻觉风险高，不进行深度搜索
        if anti_hallucination["should_avoid_deep_search"]:
            return False

        # 【新增功能1】如果检测到知识盲区，强制触发搜索
        if hasattr(self, '_current_knowledge_gaps') and self._current_knowledge_gaps.get("has_gaps", False):
            logger.info(f"🧠 智能决策：知识盲区检测，强制触发搜索")
            return True

        # 如果是随意聊天且已有基本结果，不需要深度搜索
        if user_intent["casual_level"] > 0.7 and len(result) > 10:
            return False

        # 基于权重决策
        return chat_weight > 0.45

    def _get_enhanced_decision_reason(self, user_intent: dict, final_weight: float, knowledge_gaps: dict, anti_hallucination: dict) -> str:
        """获取增强智能决策的原因 - 新增功能1"""
        if anti_hallucination["should_avoid_deep_search"]:
            return f"避免幻觉风险: {', '.join(anti_hallucination['risk_factors'])}"

        if knowledge_gaps["has_gaps"]:
            gap_info = []
            if knowledge_gaps["unknown_concepts"]:
                gap_info.append(f"未知概念: {', '.join(knowledge_gaps['unknown_concepts'][:2])}")
            if knowledge_gaps["technical_terms"]:
                gap_info.append(f"技术术语: {', '.join(knowledge_gaps['technical_terms'][:2])}")
            if knowledge_gaps["new_information"]:
                gap_info.append(f"时效信息: {', '.join(knowledge_gaps['new_information'][:2])}")

            return f"检测到知识盲区({', '.join(gap_info)})，权重: {final_weight:.2f}"

        if user_intent["casual_level"] > 0.7:
            return f"随意聊天({user_intent['type']})，权重: {final_weight:.2f}"

        return f"基于聊天需求({user_intent['type']})，权重: {final_weight:.2f}"

    def _get_intelligent_decision_reason(self, user_intent: dict, chat_weight: float, anti_hallucination: dict) -> str:
        """获取智能决策的原因 - 保持向后兼容"""
        if anti_hallucination["should_avoid_deep_search"]:
            return f"避免幻觉风险: {', '.join(anti_hallucination['risk_factors'])}"

        if user_intent["casual_level"] > 0.7:
            return f"随意聊天({user_intent['type']})，权重较低: {chat_weight:.2f}"

        return f"基于聊天需求({user_intent['type']})，权重: {chat_weight:.2f}"

    def _fallback_intelligent_analysis(self, query: str) -> dict:
        """智能分析的备用方案"""
        return {
            "query": query,
            "user_intent": {"type": "unknown", "casual_level": 0.5},
            "chat_weight": 0.3,
            "needs_deep_search": False,
            "reason": "分析失败，使用保守策略"
        }

    def _evaluate_result_completeness(self, result: str, query: str) -> float:
        """评估快速搜索结果的完整性"""
        if not result:
            return 1.0  # 无结果，需要深度搜索

        score = 0.0
        result_lower = result.lower()
        query_lower = query.lower()

        # 结果长度评估
        if len(result) < 20:
            score += 0.5  # 结果太短
        elif len(result) > 200:
            score -= 0.2  # 结果很详细

        # 关键词覆盖度评估
        query_keywords = set(query_lower.split())
        result_keywords = set(result_lower.split())
        coverage = len(query_keywords & result_keywords) / len(query_keywords)
        if coverage < 0.3:
            score += 0.4  # 覆盖度低

        # 不确定性指标
        uncertainty_words = ['可能', '据说', '传言', '好像', '似乎', '大概']
        uncertainty_count = sum(1 for word in uncertainty_words if word in result)
        if uncertainty_count > 2:
            score += 0.3  # 不确定性高

        # 具体信息检测
        if re.search(r'\d{4}年|\d+月|\d+日|\d+元|￥\d+', result):
            score -= 0.3  # 包含具体信息

        return max(0.0, min(1.0, score))

    def _determine_complexity_level(self, score: float) -> str:
        """确定复杂度级别"""
        if score <= self.complexity_levels["simple"]["threshold"]:
            return "simple"
        elif score <= self.complexity_levels["medium"]["threshold"]:
            return "medium"
        else:
            return "complex"

    def _should_trigger_deep_search(self, level: str, score: float, query: str, result: str) -> bool:
        """基于明确数值阈值决策是否需要深度搜索"""
        # 使用明确的数值阈值，而非复杂的条件判断
        DEEP_SEARCH_THRESHOLD = 0.45  # 明确的触发阈值

        # 基础评分
        trigger_score = score

        # 结果质量调整
        if len(result) < 20:
            trigger_score += 0.2  # 结果过短，增加触发概率
        elif len(result) > 100:
            trigger_score -= 0.1  # 结果详细，减少触发概率

        # 关键词缺失检测
        if "暂无" in result or "没有" in result or "未找到" in result:
            trigger_score += 0.3  # 明显信息不足

        # 明确的数值判断
        should_trigger = trigger_score > DEEP_SEARCH_THRESHOLD

        logger.info(f"深度搜索决策: 基础分数={score:.3f}, 调整后分数={trigger_score:.3f}, 阈值={DEEP_SEARCH_THRESHOLD}, 触发={should_trigger}")

        return should_trigger

    def _get_decision_reason(self, level: str, score: float, result: str) -> str:
        """获取决策原因"""
        reasons = []

        if level == "simple":
            reasons.append("简单查询")
            if len(result) < 15:
                reasons.append("结果过短")
        elif level == "medium":
            reasons.append("中等复杂查询")
            if score > 0.5:
                reasons.append("结果不够完整")
        else:
            reasons.append("复杂查询")
            reasons.append("需要深度分析")

        return "，".join(reasons)

    def _fallback_complexity_analysis(self, query: str) -> dict:
        """备用复杂度分析"""
        # 简单的备用逻辑
        if len(query) < 10 and any(word in query for word in ['什么', '多少', '时间']):
            level = "simple"
            needs_deep = False
        elif len(query) > 20 or any(word in query for word in ['为什么', '如何', '分析']):
            level = "complex"
            needs_deep = True
        else:
            level = "medium"
            needs_deep = True

        return {
            "query": query,
            "base_score": 0.5,
            "result_score": 0.5,
            "final_score": 0.5,
            "complexity_level": level,
            "needs_deep_search": needs_deep,
            "reason": "备用分析"
        }


class AntiHallucinationValidator:
    """防幻觉验证器 - 确保回复基于真实搜索结果"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model

        # 强幻觉指标（绝对禁止）
        self.strong_hallucination_indicators = [
            "我知道", "我记得", "据我了解", "根据我的知识",
            "众所周知", "显然", "毫无疑问", "肯定是",
            "一定是", "绝对是", "必然是", "当然是"
        ]

        # 编造内容指标（需要警惕）
        self.fabrication_indicators = [
            "玩家称号", "擅长搜刮资源", "机密大坝地图", "节日活动任务道具",
            "技能设计极其难", "最阴间的探员", "战术相关", "地图相关战术"
        ]

        # 游戏相关的常见编造模式
        self.gaming_fabrication_patterns = [
            r"在《.*》中.*可能指.*",
            r"游戏中.*是.*因其.*",
            r".*地图.*战术.*",
            r".*技能.*设计.*"
        ]

        # 安全的不确定性表达（推荐使用）
        self.safe_uncertainty_words = [
            "可能是", "听说是", "好像是", "应该是", "大概是",
            "传言是", "据说是", "或许是", "也许是"
        ]

    async def validate_content(self, original_query: str, search_result: str, generated_content: str) -> dict:
        """验证生成内容是否存在幻觉"""
        try:
            # 基础幻觉检测
            basic_check = self._basic_hallucination_check(generated_content)

            # 内容一致性检查
            consistency_check = await self._check_content_consistency(
                original_query, search_result, generated_content
            )

            # 综合评估
            is_valid = basic_check["is_valid"] and consistency_check["is_valid"]

            return {
                "is_valid": is_valid,
                "confidence": min(basic_check["confidence"], consistency_check["confidence"]),
                "issues": basic_check["issues"] + consistency_check["issues"],
                "corrected_content": self._correct_content(generated_content, search_result) if not is_valid else generated_content
            }

        except Exception as e:
            logger.error(f"防幻觉验证失败: {e}")
            return {
                "is_valid": False,
                "confidence": 0.0,
                "issues": ["验证过程出错"],
                "corrected_content": "搜索结果处理中遇到问题"
            }

    def _basic_hallucination_check(self, content: str) -> dict:
        """增强的基础幻觉检测"""
        issues = []
        confidence = 1.0

        # 1. 检测强幻觉指标（严重问题）
        for indicator in self.strong_hallucination_indicators:
            if indicator in content:
                issues.append(f"严重幻觉指标: {indicator}")
                confidence -= 0.5  # 严重扣分

        # 2. 检测编造内容指标（如日志中的问题）
        for fabrication in self.fabrication_indicators:
            if fabrication in content:
                issues.append(f"疑似编造内容: {fabrication}")
                confidence -= 0.4

        # 3. 检测游戏相关编造模式
        import re
        for pattern in self.gaming_fabrication_patterns:
            if re.search(pattern, content):
                issues.append(f"游戏编造模式: {pattern}")
                confidence -= 0.4

        # 4. 检测过于详细的虚假信息
        detailed_fabrication_indicators = [
            "具体数据", "详细规格", "精确数字", "官方确认",
            "内部消息", "独家消息", "权威消息"
        ]
        for indicator in detailed_fabrication_indicators:
            if indicator in content:
                issues.append(f"疑似详细编造: {indicator}")
                confidence -= 0.3

        # 5. 检测是否有安全的不确定性表达
        has_safe_uncertainty = any(word in content for word in self.safe_uncertainty_words)
        if not has_safe_uncertainty and len(content) > 15:
            issues.append("缺乏安全的不确定性表达")
            confidence -= 0.2

        # 6. 特别检查：如果内容包含具体的游戏细节但没有明确来源
        if any(word in content for word in ["技能", "装备", "地图", "战术", "玩家"]):
            if not any(word in content for word in ["据说", "听说", "可能", "传言"]):
                issues.append("游戏内容缺乏来源标注")
                confidence -= 0.3

        is_valid = confidence > 0.6  # 提高验证标准
        return {
            "is_valid": is_valid,
            "confidence": max(0.0, confidence),
            "issues": issues
        }

    async def _check_content_consistency(self, query: str, search_result: str, generated_content: str) -> dict:
        """检查内容一致性"""
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel(self.model)

            consistency_prompt = f"""检查生成内容是否与搜索结果一致，是否存在编造信息：

原始查询：{query}
搜索结果：{search_result}
生成内容：{generated_content}

请检查：
1. 生成内容是否完全基于搜索结果
2. 是否添加了搜索结果中没有的信息
3. 是否存在与搜索结果矛盾的内容
4. 是否过度解读或推测

只返回JSON格式：
{{
  "is_consistent": true/false,
  "confidence": 0.8,
  "issues": ["问题1", "问题2"],
  "fabricated_info": ["编造信息1"]
}}"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    consistency_prompt
                ),
                timeout=10.0
            )

            if response and hasattr(response, 'text') and response.text:
                try:
                    text = response.text.strip()
                    json_start = text.find('{')
                    json_end = text.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = text[json_start:json_end]
                        result = json.loads(json_str)
                        return {
                            "is_valid": result.get("is_consistent", False),
                            "confidence": result.get("confidence", 0.0),
                            "issues": result.get("issues", []) + result.get("fabricated_info", [])
                        }
                except:
                    pass

            return self._fallback_consistency_check(search_result, generated_content)

        except Exception as e:
            logger.error(f"内容一致性检查失败: {e}")
            return self._fallback_consistency_check(search_result, generated_content)

    def _fallback_consistency_check(self, search_result: str, generated_content: str) -> dict:
        """备用一致性检查"""
        issues = []
        confidence = 0.7

        # 简单的关键词匹配检查
        search_words = set(search_result.lower().split())
        generated_words = set(generated_content.lower().split())

        # 检查是否有搜索结果中没有的关键信息
        important_patterns = [r'\d{4}年', r'\d+月', r'\d+日', r'\d+元', r'官方', r'确认']
        for pattern in important_patterns:
            search_matches = re.findall(pattern, search_result)
            generated_matches = re.findall(pattern, generated_content)

            # 如果生成内容中有搜索结果中没有的重要信息
            for match in generated_matches:
                if match not in search_matches:
                    issues.append(f"可能编造了信息: {match}")
                    confidence -= 0.2

        # 检查长度比例
        if len(generated_content) > len(search_result) * 1.5:
            issues.append("生成内容明显超出搜索结果范围")
            confidence -= 0.3

        is_valid = confidence > 0.4
        return {
            "is_valid": is_valid,
            "confidence": max(0.0, confidence),
            "issues": issues
        }

    def _correct_content(self, generated_content: str, search_result: str) -> str:
        """修正有问题的内容"""
        # 如果生成内容有问题，直接基于搜索结果生成安全的回复
        if not search_result:
            return "没有找到相关信息"

        # 提取搜索结果的核心信息
        core_info = search_result[:50] + ('...' if len(search_result) > 50 else '')

        # 添加不确定性表达
        uncertainty_starters = ["据搜索结果显示", "搜索到的信息是", "找到的资料显示"]
        import random
        starter = random.choice(uncertainty_starters)

        return f"{starter}，{core_info}"


class IntelligentContentAnalyzer:
    """智能内容分析器 - 基于权重值动态分析和输出"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model
        self.analysis_cache: Dict[str, Any] = {}
        self.complexity_analyzer = DynamicWeightAnalyzer()

    async def analyze_query_intent(self, query: str) -> dict:
        """分析用户查询意图和权重"""
        try:
            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel(self.model)

            intent_prompt = f"""分析用户查询的意图和重要性权重：

用户查询：{query}

请分析：
1. 查询类型（时间、价格、定义、方法、链接、一般）
2. 紧急程度（1-10分）
3. 信息深度需求（简单/详细/深入）
4. 关键概念权重（列出3个最重要的概念及权重0-1）

只返回JSON格式：
{{
  "type": "查询类型",
  "urgency": 数字,
  "depth": "信息深度",
  "concepts": [{{"concept": "概念", "weight": 0.8}}]
}}"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    intent_prompt
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                try:
                    # 提取JSON部分
                    text = response.text.strip()
                    json_start = text.find('{')
                    json_end = text.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = text[json_start:json_end]
                        return json.loads(json_str)
                except:
                    pass

            # 备用分析
            return self._fallback_intent_analysis(query)

        except Exception as e:
            logger.error(f"查询意图分析失败: {e}")
            return self._fallback_intent_analysis(query)

    def _fallback_intent_analysis(self, query: str) -> dict:
        """备用意图分析"""
        query_lower = query.lower()

        # 简单的类型判断
        if any(word in query_lower for word in ['时间', '日期', '什么时候', '何时']):
            query_type = "时间查询"
            urgency = 7
        elif any(word in query_lower for word in ['价格', '多少钱', '费用']):
            query_type = "价格查询"
            urgency = 6
        elif any(word in query_lower for word in ['链接', '网址', '地址']):
            query_type = "链接查询"
            urgency = 8
        else:
            query_type = "一般查询"
            urgency = 5

        # 提取关键概念
        words = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+\d*', query)
        concepts = [{"concept": word, "weight": 0.7} for word in words[:3] if len(word) > 1]

        return {
            "type": query_type,
            "urgency": urgency,
            "depth": "详细" if urgency > 6 else "简单",
            "concepts": concepts
        }

    async def analyze_content_weight(self, content: str, query_intent: dict) -> dict:
        """分析内容权重和重要性"""
        try:
            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            weight_prompt = f"""根据用户查询意图分析内容的重要性权重：

用户查询类型：{query_intent.get('type', '一般查询')}
用户关注概念：{[c['concept'] for c in query_intent.get('concepts', [])]}
内容：{content[:1000]}

请分析：
1. 内容相关度（0-1）
2. 信息价值（0-1）
3. 时效性（0-1）
4. 是否需要多次回复（true/false）
5. 回复风格（casual/formal/excited）

只返回JSON：
{{
  "relevance": 0.8,
  "value": 0.9,
  "timeliness": 0.7,
  "multiple_replies": true,
  "style": "casual"
}}"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=self.model,
                    contents=weight_prompt,
                    config=config
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                try:
                    text = response.text.strip()
                    json_start = text.find('{')
                    json_end = text.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = text[json_start:json_end]
                        return json.loads(json_str)
                except:
                    pass

            return self._fallback_weight_analysis(content)

        except Exception as e:
            logger.error(f"内容权重分析失败: {e}")
            return self._fallback_weight_analysis(content)

    def _fallback_weight_analysis(self, content: str) -> dict:
        """备用权重分析"""
        # 简单的权重计算
        relevance = min(len(content) / 500, 1.0)
        value = 0.7 if len(content) > 100 else 0.4
        timeliness = 0.8 if any(word in content for word in ['最新', '今天', '刚刚', '官方']) else 0.5
        multiple_replies = len(content) > 300

        return {
            "relevance": relevance,
            "value": value,
            "timeliness": timeliness,
            "multiple_replies": multiple_replies,
            "style": "casual"
        }


class NaturalInteractionGenerator:
    """自然交互生成器 - 基于内容智能决定语气和场景"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model
        self.reply_history = []

        # 【新增功能4】动态回复风格配置
        self.dynamic_style_enabled = True
        self.natural_chat_mode = True
        self.formal_reply_suppression = True
        self.user_tone_analysis = True
        self.reply_depth_adaptation = True
        self.maintain_casual_expressions = True

        # 不同场景的语气模板
        self.interaction_scenarios = {
            "surprising": ["哇", "卧槽", "真的假的", "不会吧", "这也太", "我去"],
            "funny": ["哈哈", "笑死", "绝了", "太逗了", "这什么鬼", "离谱"],
            "awkward": ["emmm", "这个嘛", "咋说呢", "有点尴尬", "这就", "额"],
            "excited": ["太棒了", "牛逼", "厉害", "爽", "赞", "给力"],
            "disappointed": ["唉", "算了", "没意思", "boring", "就这", "一般般"],
            "confused": ["啥", "什么鬼", "看不懂", "迷惑", "咋回事", "奇怪"],
            "casual": ["嗯", "哦", "行吧", "还行", "可以", "不错"]
        }

        # 【新增功能4】用户语气分析模式
        self.user_tone_patterns = {
            "very_casual": {
                "patterns": ["啥", "咋", "嘛", "哈哈", "笑死", "绝了", "牛逼", "卧槽"],
                "formality_level": 0.1,
                "reply_style": "super_casual"
            },
            "casual": {
                "patterns": ["什么", "怎么", "好的", "行", "可以", "不错", "还行"],
                "formality_level": 0.3,
                "reply_style": "casual"
            },
            "neutral": {
                "patterns": ["请问", "能否", "如何", "是否", "关于", "请"],
                "formality_level": 0.5,
                "reply_style": "neutral"
            },
            "formal": {
                "patterns": ["您好", "请您", "麻烦", "谢谢", "不好意思", "打扰"],
                "formality_level": 0.8,
                "reply_style": "polite"
            }
        }

    def analyze_user_tone(self, query: str) -> dict:
        """分析用户语气和正式程度 - 新增功能4"""
        if not self.user_tone_analysis:
            return {"tone": "casual", "formality_level": 0.3, "reply_style": "casual"}

        query_lower = query.lower()
        tone_scores = {}

        # 分析各种语气模式的匹配度
        for tone_type, config in self.user_tone_patterns.items():
            score = 0
            for pattern in config["patterns"]:
                if pattern in query_lower:
                    score += 1

            if score > 0:
                tone_scores[tone_type] = {
                    "score": score,
                    "formality_level": config["formality_level"],
                    "reply_style": config["reply_style"]
                }

        # 选择最匹配的语气
        if tone_scores:
            best_tone = max(tone_scores.keys(), key=lambda x: tone_scores[x]["score"])
            return {
                "tone": best_tone,
                "formality_level": tone_scores[best_tone]["formality_level"],
                "reply_style": tone_scores[best_tone]["reply_style"]
            }

        # 默认随意语气
        return {"tone": "casual", "formality_level": 0.3, "reply_style": "casual"}

    def adapt_reply_style(self, content: str, user_tone: dict, query: str) -> str:
        """根据用户语气动态适配回复风格 - 新增功能4"""
        if not self.dynamic_style_enabled:
            return content

        # 获取用户的正式程度
        formality_level = user_tone.get("formality_level", 0.3)
        reply_style = user_tone.get("reply_style", "casual")

        # 根据正式程度调整回复
        if reply_style == "super_casual" and self.maintain_casual_expressions:
            # 超级随意：使用最自然的表达
            adapted = self._apply_super_casual_style(content, query)
        elif reply_style == "casual":
            # 随意：保持自然但稍微规范
            adapted = self._apply_casual_style(content, query)
        elif reply_style == "neutral":
            # 中性：平衡的表达方式
            adapted = self._apply_neutral_style(content, query)
        elif reply_style == "polite":
            # 礼貌：但仍然避免过度正式
            adapted = self._apply_polite_style(content, query)
        else:
            adapted = content

        # 应用正式回复抑制
        if self.formal_reply_suppression:
            adapted = self._suppress_formal_expressions(adapted)

        return adapted

    def _apply_super_casual_style(self, content: str, query: str) -> str:
        """应用超级随意风格 - 新增功能4"""
        if not content:
            return "没找到相关信息哦"

        # 添加随意的语气词
        casual_endings = ["哦", "呢", "的说", "那种", "就是这样"]
        import random

        # 如果内容没有自然的结尾，添加一个
        if not any(ending in content for ending in ["哦", "呢", "的", "那种", "就是"]):
            content += random.choice(casual_endings)

        return content

    def _apply_casual_style(self, content: str, query: str) -> str:
        """应用随意风格 - 新增功能4"""
        if not content:
            return "没找到相关信息"

        # 保持现有的自然表达，稍作调整
        return content

    def _apply_neutral_style(self, content: str, query: str) -> str:
        """应用中性风格 - 新增功能4"""
        if not content:
            return "暂时没有找到相关信息"

        # 稍微正式一点，但避免过度
        return content

    def _apply_polite_style(self, content: str, query: str) -> str:
        """应用礼貌风格 - 新增功能4"""
        if not content:
            return "抱歉，暂时没有找到相关信息"

        # 礼貌但不过分正式
        return content

    def _suppress_formal_expressions(self, content: str) -> str:
        """抑制过于正式的表达 - 新增功能4"""
        if not self.formal_reply_suppression:
            return content

        # 替换过于正式的表达
        formal_replacements = {
            "根据搜索结果显示": "",
            "经过查询发现": "",
            "通过搜索了解到": "",
            "据相关资料显示": "",
            "根据相关信息": "",
            "经过分析得出": "",
            "综合各方面信息": "",
            "通过深入了解": ""
        }

        for formal, replacement in formal_replacements.items():
            content = content.replace(formal, replacement)

        # 清理多余的空格
        content = re.sub(r'\s+', ' ', content).strip()

        return content

    async def generate_single_concise_reply(self, content: str, weight_analysis: dict, query: str) -> str:
        """生成单次简洁回复 - 增强动态风格适配版"""
        try:
            if not weight_analysis.get("multiple_replies", False):
                return ""

            # 【新增功能4】分析用户语气
            user_tone = self.analyze_user_tone(query)

            # 提取核心信息，避免过度润色
            core_info = self._extract_core_information(content, query)

            # 简洁格式化
            formatted_reply = self._format_concise_reply(core_info, query)

            # 【新增功能4】动态风格适配
            adapted_reply = self.adapt_reply_style(formatted_reply, user_tone, query)

            return adapted_reply

        except Exception as e:
            logger.error(f"简洁回复生成失败: {e}")
            return self._fallback_concise_reply(content)

    def _extract_core_information(self, content: str, query: str) -> str:
        """提取核心信息，保持详细程度"""
        if not content:
            return ""

        # 移除格式符号
        clean_content = re.sub(r'[🔍📋🔗📈💡⏰✅❌😊😄🎉👍]', '', content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()

        # 保持更多详细信息，提高精准度
        # 如果内容本身就很详细，保留更多信息
        if len(clean_content) <= 200:
            return clean_content  # 短内容直接返回

        # 对于长内容，提取关键段落而非简单截取
        sentences = clean_content.split('。')
        important_sentences = []

        # 优先保留包含关键信息的句子
        priority_keywords = [
            '官方', '发布', '时间', '日期', '价格', '确认', '公布',
            '预计', '计划', '预期', '最新', '消息', '通知'
        ]

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # 优先选择包含关键词的句子
            if any(keyword in sentence for keyword in priority_keywords):
                important_sentences.append(sentence)
            elif len(important_sentences) < 3 and len(sentence) > 10:
                important_sentences.append(sentence)

        # 如果没有找到关键句子，保留前3个句子
        if not important_sentences:
            important_sentences = sentences[:3]

        result = '。'.join(important_sentences[:4])  # 最多4个句子，保持详细

        # 确保不超过合理长度
        if len(result) > 300:
            result = result[:300] + '...'

        return result

    def _format_concise_reply(self, core_info: str, query: str) -> str:
        """格式化为自然聊天风格"""
        if not core_info:
            return ""

        # 直接返回核心信息，保持自然聊天感觉
        return core_info

    def _fallback_concise_reply(self, content: str) -> str:
        """备用简洁回复"""
        if not content:
            return ""

        # 简单截取，保持factual presentation
        clean_content = re.sub(r'[🔍📋🔗📈💡⏰✅❌😊😄🎉👍]', '', content)
        if len(clean_content) <= 60:
            return clean_content
        else:
            return clean_content[:60] + "..."

    async def _analyze_content_scenario(self, content: str, query: str) -> dict:
        """分析内容的情感场景和应该采用的交互方式"""
        try:
            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            scenario_prompt = f"""分析以下搜索内容应该用什么语气和场景来回复：

用户问题：{query}
搜索内容：{content}

请分析内容特点并选择最合适的交互场景：

1. surprising（惊讶）- 内容很意外、震惊
2. funny（搞笑）- 内容很有趣、好笑
3. awkward（尴尬）- 内容让人无语、尴尬
4. excited（兴奋）- 内容很棒、令人兴奋
5. disappointed（失望）- 内容不如预期、失望
6. confused（困惑）- 内容复杂、让人困惑
7. casual（随意）- 内容普通、日常聊天

只返回JSON格式：
{{
  "scenario": "场景名称",
  "intensity": 0.8,
  "reason": "选择理由",
  "key_points": ["关键点1", "关键点2"]
}}"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=self.model,
                    contents=scenario_prompt,
                    config=config
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                try:
                    text = response.text.strip()
                    json_start = text.find('{')
                    json_end = text.rfind('}') + 1
                    if json_start >= 0 and json_end > json_start:
                        json_str = text[json_start:json_end]
                        return json.loads(json_str)
                except:
                    pass

            return self._fallback_scenario_analysis(content)

        except Exception as e:
            logger.error(f"场景分析失败: {e}")
            return self._fallback_scenario_analysis(content)

    def _fallback_scenario_analysis(self, content: str) -> dict:
        """备用场景分析"""
        content_lower = content.lower()

        # 简单的关键词匹配
        if any(word in content_lower for word in ['震惊', '意外', '没想到', '竟然']):
            return {"scenario": "surprising", "intensity": 0.7, "reason": "内容令人意外", "key_points": []}
        elif any(word in content_lower for word in ['搞笑', '有趣', '好玩', '逗']):
            return {"scenario": "funny", "intensity": 0.6, "reason": "内容有趣", "key_points": []}
        elif any(word in content_lower for word in ['尴尬', '无语', '奇怪']):
            return {"scenario": "awkward", "intensity": 0.5, "reason": "内容尴尬", "key_points": []}
        elif any(word in content_lower for word in ['棒', '厉害', '牛', '赞']):
            return {"scenario": "excited", "intensity": 0.8, "reason": "内容令人兴奋", "key_points": []}
        elif any(word in content_lower for word in ['失望', '没意思', '一般']):
            return {"scenario": "disappointed", "intensity": 0.6, "reason": "内容令人失望", "key_points": []}
        elif any(word in content_lower for word in ['复杂', '困惑', '不懂']):
            return {"scenario": "confused", "intensity": 0.5, "reason": "内容复杂", "key_points": []}
        else:
            return {"scenario": "casual", "intensity": 0.4, "reason": "普通内容", "key_points": []}

    async def _generate_scenario_based_replies(self, content: str, scenario: dict, query: str, weight_analysis: dict) -> List[str]:
        """基于场景生成自然回复"""
        try:
            scenario_name = scenario.get("scenario", "casual")
            intensity = scenario.get("intensity", 0.5)
            key_points = scenario.get("key_points", [])

            # 根据场景选择语气词
            tone_words = self.interaction_scenarios.get(scenario_name, self.interaction_scenarios["casual"])

            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            reply_count = self._calculate_reply_count(weight_analysis)
            replies = []

            for i in range(reply_count):
                # 为每次回复选择不同的语气词
                import random
                tone_word = random.choice(tone_words)

                reply_prompt = f"""基于场景生成第{i+1}次自然聊天回复：

用户问的：{query}
搜索内容：{content}
场景类型：{scenario_name}（{scenario.get('reason', '')}）
语气强度：{intensity}
建议语气词：{tone_word}
关键点：{key_points}
已有回复：{replies}

生成要求：
1. 用{scenario_name}场景的自然语气
2. 可以用"{tone_word}"开头，但要自然
3. 基于搜索内容，不要编造
4. 像真正的朋友聊天一样
5. 每次回复都要不同
6. 控制在20字以内
7. 不要表情符号

第{i+1}次{scenario_name}风格回复："""

                response = await asyncio.wait_for(
                    asyncio.to_thread(
                        client.models.generate_content,
                        model=self.model,
                        contents=reply_prompt,
                        config=config
                    ),
                    timeout=8.0
                )

                if response and hasattr(response, 'text') and response.text:
                    reply = response.text.strip()
                    # 清理格式
                    reply = re.sub(r'[🔍📋🔗📈💡⏰✅❌😊😄🎉👍]', '', reply)
                    reply = reply.strip('"\'""''')

                    if reply and reply not in replies and len(reply) <= 30:
                        replies.append(reply)

                await asyncio.sleep(1)

            return replies

        except Exception as e:
            logger.error(f"场景回复生成失败: {e}")
            return self._fallback_natural_replies(content, weight_analysis)

    def _fallback_natural_replies(self, content: str, weight_analysis: dict) -> List[str]:
        """备用自然回复生成"""
        if len(content) < 50:
            return []

        # 根据内容特点选择自然回复
        content_lower = content.lower()

        if '官方' in content_lower and '发布' in content_lower:
            return ["官方终于说话了"]
        elif '传言' in content_lower or '据说' in content_lower:
            return ["这个传言我也听过"]
        elif '价格' in content_lower:
            return ["价格还行吧"]
        elif '时间' in content_lower or '日期' in content_lower:
            return ["时间定了就好"]
        else:
            natural_responses = [
                "这样啊",
                "原来如此",
                "了解了",
                "知道了",
                "还不错"
            ]
            import random
            return [random.choice(natural_responses)]

    def _calculate_reply_count(self, weight_analysis: dict) -> int:
        """根据权重计算回复次数"""
        base_count = 1

        # 根据相关度增加回复次数
        if weight_analysis.get("relevance", 0) > 0.8:
            base_count += 1

        # 根据信息价值增加回复次数
        if weight_analysis.get("value", 0) > 0.8:
            base_count += 1

        # 根据时效性增加回复次数
        if weight_analysis.get("timeliness", 0) > 0.8:
            base_count += 1

        return min(base_count, 3)  # 最多3次回复

    def _fallback_replies(self, content: str, weight_analysis: dict) -> List[str]:
        """备用回复生成"""
        if len(content) < 100:
            return []

        # 简单的多样化回复
        starters = [
            "哦对了，",
            "想起来了，",
            "还有个事，",
            "顺便说一下，",
            ""
        ]

        import random
        starter = random.choice(starters)

        # 提取关键信息
        key_info = content[:50] + ('...' if len(content) > 50 else '')

        return [f"{starter}{key_info}"]


class IntelligentContentProcessor:
    """智能内容处理器 - 提取重要数据并进行自然语音润色"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model
        self.max_length = 200  # 增加到200字符，允许更自然的表达

    async def process_content(self, original_query: str, search_result: str, link_analyzer=None) -> str:
        """智能处理内容，提取重要数据并润色，支持链接解析"""
        if not search_result:
            return ""

        # 提取链接
        links = self._extract_links(search_result)

        # 智能提取重要数据
        important_data = await self._extract_important_data(original_query, search_result)

        # 如果有链接解析器，尝试解析链接内容
        link_analysis = ""
        if link_analyzer and links:
            for link in links[:1]:  # 只解析第一个链接
                try:
                    analysis_result = await link_analyzer.analyze_link(link)
                    if analysis_result["success"]:
                        link_analysis = analysis_result["content"]
                        logger.info(f"链接解析成功: {link}")
                        break
                except Exception as e:
                    logger.error(f"链接解析失败: {link} - {e}")

        # 合并搜索结果和链接分析
        combined_data = important_data
        if link_analysis:
            combined_data = f"{important_data}，{link_analysis}" if important_data else link_analysis

        # 自然语音润色
        polished_content = await self._natural_polish(original_query, combined_data)

        # 添加链接（如果有且需要）
        final_content = self._add_links_if_needed(polished_content, links)

        return final_content

    def _extract_links(self, content: str) -> list:
        """提取内容中的链接"""
        # 提取HTTP/HTTPS链接
        url_pattern = r'https?://[^\s\u4e00-\u9fff]+'
        links = re.findall(url_pattern, content)
        return links[:2]  # 最多保留2个链接

    async def _extract_important_data(self, query: str, content: str) -> str:
        """使用AI智能提取重要数据 - 针对性提取"""
        try:
            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            # 分析查询类型，提供更针对性的提取指导
            query_type = self._analyze_query_type(query)

            extraction_prompt = f"""从搜索结果中提取真实可靠的核心信息：

用户问题：{query}
搜索结果：{content}

问题类型：{query_type}
提取重点：{self._get_extraction_focus(query_type)}

严格要求：
- 只提取确实存在于搜索结果中的信息
- 如果信息不确定，必须标注"据说"、"可能"、"传言"
- 不要添加搜索结果中没有的内容
- 如果没有找到相关信息，回答"暂无相关信息"
- 保持客观中性，不要夸大或美化
- 严格控制在50字以内
- 用简洁的事实陈述

提取的核心信息："""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=self.model,
                    contents=extraction_prompt,
                    config=config
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                extracted = response.text.strip()
                # 清理可能的格式符号
                extracted = re.sub(r'[🔍📋🔗📈💡⏰✅❌]', '', extracted)
                return extracted
            else:
                return self._fallback_extract(content)

        except Exception as e:
            logger.error(f"智能数据提取失败: {e}")
            return self._fallback_extract(content)

    def _analyze_query_type(self, query: str) -> str:
        """分析查询类型"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['时间', '日期', '什么时候', '何时', '发布']):
            return "时间查询"
        elif any(word in query_lower for word in ['价格', '多少钱', '费用', '成本']):
            return "价格查询"
        elif any(word in query_lower for word in ['是什么', '什么是', '介绍', '定义']):
            return "定义查询"
        elif any(word in query_lower for word in ['怎么', '如何', '方法', '步骤']):
            return "方法查询"
        elif any(word in query_lower for word in ['链接', '网址', '地址', 'url']):
            return "链接查询"
        else:
            return "一般查询"

    def _get_extraction_focus(self, query_type: str) -> str:
        """根据查询类型获取提取重点"""
        focus_map = {
            "时间查询": "- 具体的发布时间、日期\n- 时间相关的官方公告",
            "价格查询": "- 具体价格数字\n- 费用相关信息",
            "定义查询": "- 准确的定义或解释\n- 核心特征描述",
            "方法查询": "- 具体的操作步骤\n- 实用的方法建议",
            "链接查询": "- 官方网址或相关链接\n- 平台地址信息",
            "一般查询": "- 最直接的答案\n- 关键事实信息"
        }
        return focus_map.get(query_type, "- 最相关的核心信息")

    def _fallback_extract(self, content: str) -> str:
        """备用数据提取方案 - 优化版"""
        # 移除所有表情符号和格式符号
        clean_content = re.sub(r'[#*`_\[\]()📋🔗📈💡⏰✅❌🔍]', '', content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()

        # 寻找关键信息 - 更精准的模式
        key_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日[^。]*)',  # 完整日期信息
            r'(官方[确认宣布表示][^。]*)',  # 官方确认信息
            r'(已[确认发布上线][^。]*)',  # 确认发布信息
            r'([预计预期].*?[发布上线][^。]*)',  # 预期发布信息
            r'(价格.*?\d+[元块][^。]*)',  # 价格信息
        ]

        extracted_info = []
        for pattern in key_patterns:
            matches = re.findall(pattern, clean_content)
            if matches:
                extracted_info.extend(matches[:1])

        if extracted_info:
            result = '，'.join(extracted_info)
            return result[:60] + ('...' if len(result) > 60 else '')
        else:
            # 提取前60字符，但保持句子完整
            if len(clean_content) <= 60:
                return clean_content
            else:
                truncated = clean_content[:60]
                last_punct = max(truncated.rfind('。'), truncated.rfind('，'), truncated.rfind('；'))
                if last_punct > 30:  # 如果标点位置合理
                    return truncated[:last_punct + 1]
                else:
                    return truncated + '...'

    def _fallback_extract(self, content: str) -> str:
        """备用数据提取方案"""
        # 移除格式符号
        clean_content = re.sub(r'[#*`_\[\]()📋🔗📈💡⏰]', '', content)
        clean_content = re.sub(r'\s+', ' ', clean_content).strip()

        # 寻找关键信息
        key_patterns = [
            r'(\d{4}年\d{1,2}月\d{1,2}日)',  # 日期
            r'(官方[^。]*)',  # 官方信息
            r'(确认[^。]*)',  # 确认信息
            r'(发布[^。]*)',  # 发布信息
            r'(最新[^。]*)',  # 最新信息
        ]

        extracted_info = []
        for pattern in key_patterns:
            matches = re.findall(pattern, clean_content)
            extracted_info.extend(matches[:1])  # 每种类型最多1个

        if extracted_info:
            return '，'.join(extracted_info)
        else:
            # 如果没有找到关键信息，返回前80字符
            return clean_content[:80] + ('...' if len(clean_content) > 80 else '')

    async def _natural_polish(self, query: str, important_data: str) -> str:
        """自然语音润色 - 真正的口语化表达"""
        try:
            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            # 先分析信息的特点来决定语气
            info_analysis = await self._analyze_info_characteristics(important_data, query)

            polish_prompt = f"""将搜索信息转换成真正自然的日常聊天表达：

用户问的：{query}
搜索信息：{important_data}
信息特点：{info_analysis}

转换要求：
1. 根据信息特点选择合适的语气（惊讶/搞笑/尴尬/兴奋/失望/困惑/随意）
2. 像真正的朋友聊天，不要模版化
3. 可以用"哇"、"卧槽"、"emmm"、"哈哈"等真实语气词
4. 不要用"好像"、"据说"、"可能"这些模版词汇
5. 基于搜索信息，但要自然表达
6. 控制在30字以内
7. 不要表情符号

真正自然的聊天表达："""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=self.model,
                    contents=polish_prompt,
                    config=config
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                polished = response.text.strip()
                # 清理格式和引号
                polished = polished.strip('"\'""''')
                # 移除所有表情符号
                polished = re.sub(r'[🔍📋🔗📈💡⏰✅❌😊😄🎉👍]', '', polished)
                # 移除AI感强的词汇
                polished = re.sub(r'(根据搜索结果|经过查询|通过搜索|搜索显示)', '', polished)
                polished = polished.strip('，。')
                return polished
            else:
                return self._fallback_polish(important_data)

        except Exception as e:
            logger.error(f"自然语音润色失败: {e}")
            return self._fallback_polish(important_data)

    async def _analyze_info_characteristics(self, info: str, query: str) -> str:
        """分析信息特点来决定语气"""
        try:
            if not info:
                return "无信息"

            info_lower = info.lower()
            query_lower = query.lower()

            # 分析信息特点
            characteristics = []

            if any(word in info_lower for word in ['震惊', '意外', '没想到', '竟然', '居然']):
                characteristics.append("令人意外")
            if any(word in info_lower for word in ['搞笑', '有趣', '好玩', '逗', '笑']):
                characteristics.append("有趣好笑")
            if any(word in info_lower for word in ['尴尬', '无语', '奇怪', '离谱']):
                characteristics.append("让人无语")
            if any(word in info_lower for word in ['棒', '厉害', '牛', '赞', '给力']):
                characteristics.append("令人兴奋")
            if any(word in info_lower for word in ['失望', '没意思', '一般', 'boring']):
                characteristics.append("令人失望")
            if any(word in info_lower for word in ['复杂', '困惑', '不懂', '迷惑']):
                characteristics.append("复杂困惑")

            # 特殊情况分析
            if '官方' in info_lower and ('未' in info_lower or '没' in info_lower):
                characteristics.append("官方还没消息")
            if '传言' in info_lower or '据说' in info_lower:
                characteristics.append("只是传言")
            if len(info) < 20:
                characteristics.append("信息很少")

            return "，".join(characteristics) if characteristics else "普通信息"

        except Exception as e:
            logger.error(f"信息特点分析失败: {e}")
            return "普通信息"

    def _fallback_polish(self, data: str) -> str:
        """备用润色方案 - 真正自然的处理"""
        if not data:
            natural_no_info = ["没找到啥", "搜不到", "没消息", "查不到", "暂时没有"]
            import random
            return random.choice(natural_no_info)

        # 移除表情符号
        polished = re.sub(r'[🔍📋🔗📈💡⏰✅❌😊😄🎉👍]', '', data)

        # 根据内容特点添加自然语气
        data_lower = polished.lower()

        if '官方' in data_lower and ('未' in data_lower or '没' in data_lower):
            natural_starters = ["官方还没说", "官方没消息", "还没官宣"]
        elif '传言' in data_lower:
            natural_starters = ["听说是", "传言说", "有人说"]
        elif '价格' in data_lower:
            natural_starters = ["价格是", "要", "得花"]
        elif '时间' in data_lower or '日期' in data_lower:
            natural_starters = ["时间是", "定在", ""]
        else:
            natural_starters = ["", "就是", "应该是"]

        import random
        starter = random.choice(natural_starters)

        # 简化内容
        if len(polished) > 25:
            polished = polished[:25] + "..."

        result = f"{starter}{polished}" if starter else polished
        return result.strip()

    def _add_links_if_needed(self, content: str, links: list) -> str:
        """如果需要，自然地添加链接"""
        if not links:
            return content

        # 检查是否需要添加链接
        needs_link = (
            len(content) < 30 or  # 内容很短
            any(word in content.lower() for word in ['链接', '网址', '地址', 'url', '官网', '网站']) or
            any(word in links[0].lower() for word in ['bilibili', 'b站', 'weibo', '微博', 'official'])  # 常见平台
        )

        if needs_link:
            # 自然地添加链接，不使用表情符号
            return f"{content} {links[0]}"

        return content


class IntelligentLinkAnalyzer:
    """智能链接解析器 - 自动分析链接内容，排除敏感网站"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model

        # 【新增功能2】链接内容优先处理配置
        self.link_priority_enabled = True
        self.link_weight_boost = 1.5
        self.bypass_length_limit = True
        self.link_processing_timeout = 15
        self.priority_link_cache = {}  # 优先链接缓存

        # 敏感网站黑名单
        self.blocked_domains = {
            # 成人内容
            'pornhub.com', 'xvideos.com', 'xhamster.com', 'redtube.com',
            # 赌博网站
            'bet365.com', 'casino.com', 'poker.com',
            # 暗网相关
            '.onion',
            # 恶意软件
            'malware.com', 'virus.com',
            # 其他敏感内容
            'piratebay.org', 'torrent.com'
        }

        # 支持的安全域名
        self.safe_domains = {
            'bilibili.com', 'b23.tv', 'youtube.com', 'youtu.be',
            'github.com', 'stackoverflow.com', 'zhihu.com',
            'baidu.com', 'google.com', 'wikipedia.org',
            'weibo.com', 'douyin.com', 'tiktok.com',
            'qq.com', 'wechat.com', 'taobao.com', 'tmall.com',
            'jd.com', 'amazon.com', 'netflix.com'
        }

    def is_safe_url(self, url: str) -> bool:
        """检查URL是否安全"""
        try:
            url_lower = url.lower()

            # 检查黑名单
            for blocked in self.blocked_domains:
                if blocked in url_lower:
                    return False

            # 检查是否为HTTPS（更安全）
            if not url_lower.startswith(('http://', 'https://')):
                return False

            # 检查是否为已知安全域名
            for safe_domain in self.safe_domains:
                if safe_domain in url_lower:
                    return True

            # 对于未知域名，采用保守策略
            return True  # 可以根据需要调整为False

        except Exception as e:
            logger.error(f"URL安全检查失败: {e}")
            return False

    async def analyze_link(self, url: str) -> dict:
        """分析链接内容"""
        try:
            # 安全检查
            if not self.is_safe_url(url):
                return {
                    "success": False,
                    "reason": "链接被安全策略阻止",
                    "content": ""
                }

            # 获取网页内容
            page_content = await self._fetch_page_content(url)
            if not page_content:
                return {
                    "success": False,
                    "reason": "无法获取网页内容",
                    "content": ""
                }

            # AI分析内容
            analysis = await self._analyze_content(url, page_content)

            return {
                "success": True,
                "reason": "分析成功",
                "content": analysis,
                "url": url
            }

        except Exception as e:
            logger.error(f"链接分析失败: {url} - {e}")
            return {
                "success": False,
                "reason": f"分析出错: {str(e)}",
                "content": ""
            }

    def detect_shared_links(self, query: str) -> list:
        """检测用户分享的链接 - 新增功能2"""
        import re

        # 匹配各种链接格式
        link_patterns = [
            r'https?://[^\s]+',  # 标准HTTP链接
            r'www\.[^\s]+',      # www开头的链接
            r'[a-zA-Z0-9-]+\.[a-zA-Z]{2,}[^\s]*',  # 域名格式
        ]

        links = []
        for pattern in link_patterns:
            matches = re.findall(pattern, query)
            links.extend(matches)

        # 清理和验证链接
        cleaned_links = []
        for link in links:
            # 确保链接有协议
            if not link.startswith(('http://', 'https://')):
                if link.startswith('www.'):
                    link = 'https://' + link
                else:
                    link = 'https://' + link

            # 安全检查
            if self.is_safe_url(link):
                cleaned_links.append(link)

        return cleaned_links

    async def priority_link_processing(self, query: str, links: list) -> dict:
        """优先处理用户分享的链接 - 新增功能2"""
        if not self.link_priority_enabled or not links:
            return {"has_priority_content": False, "content": "", "processed_links": []}

        priority_results = []
        processed_links = []

        for link in links[:3]:  # 最多处理3个链接
            try:
                # 检查缓存
                if link in self.priority_link_cache:
                    cached_result = self.priority_link_cache[link]
                    priority_results.append(cached_result)
                    processed_links.append(link)
                    continue

                # 优先处理链接内容
                link_analysis = await asyncio.wait_for(
                    self.analyze_link(link),
                    timeout=self.link_processing_timeout
                )

                if link_analysis["success"]:
                    # 应用权重提升
                    enhanced_content = self._enhance_link_content(link_analysis["content"], link)

                    # 缓存结果
                    self.priority_link_cache[link] = enhanced_content

                    priority_results.append(enhanced_content)
                    processed_links.append(link)

            except asyncio.TimeoutError:
                logger.warning(f"链接处理超时: {link}")
                continue
            except Exception as e:
                logger.error(f"链接优先处理失败: {link} - {e}")
                continue

        # 合并优先内容
        if priority_results:
            combined_content = self._combine_priority_content(priority_results, query)
            return {
                "has_priority_content": True,
                "content": combined_content,
                "processed_links": processed_links,
                "weight_boost": self.link_weight_boost
            }

        return {"has_priority_content": False, "content": "", "processed_links": []}

    def _enhance_link_content(self, content: str, link: str) -> str:
        """增强链接内容展示 - 新增功能2"""
        if not content:
            return f"链接内容暂时无法获取: {link}"

        # 如果启用了绕过长度限制
        if self.bypass_length_limit:
            # 保持完整内容，只做基本清理
            enhanced = content.strip()
        else:
            # 应用标准长度限制
            enhanced = content[:500] + ('...' if len(content) > 500 else '')

        # 添加链接来源标注
        enhanced = f"{enhanced}\n\n📎 来源: {link}"

        return enhanced

    def _combine_priority_content(self, results: list, query: str) -> str:
        """合并优先内容 - 新增功能2"""
        if len(results) == 1:
            return results[0]

        # 多个链接内容的智能合并
        combined = "📋 链接内容汇总:\n\n"
        for i, result in enumerate(results, 1):
            combined += f"{i}. {result}\n\n"

        return combined.strip()

    async def _fetch_page_content(self, url: str) -> str:
        """获取网页内容"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            timeout = aiohttp.ClientTimeout(total=10)  # 10秒超时

            async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        # 提取主要文本内容（简单的HTML清理）
                        clean_content = self._clean_html(content)
                        return clean_content[:2000]  # 限制长度
                    else:
                        logger.warning(f"HTTP {response.status}: {url}")
                        return ""

        except asyncio.TimeoutError:
            logger.warning(f"获取网页超时: {url}")
            return ""
        except Exception as e:
            logger.error(f"获取网页内容失败: {url} - {e}")
            return ""

    def _clean_html(self, html_content: str) -> str:
        """简单的HTML清理"""
        # 移除脚本和样式
        html_content = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        html_content = re.sub(r'<style[^>]*>.*?</style>', '', html_content, flags=re.DOTALL | re.IGNORECASE)

        # 移除HTML标签
        html_content = re.sub(r'<[^>]+>', ' ', html_content)

        # 清理空白字符
        html_content = re.sub(r'\s+', ' ', html_content)

        return html_content.strip()

    async def _analyze_content(self, url: str, content: str) -> str:
        """AI分析网页内容"""
        try:
            client = genai.Client(api_key=self.api_key)
            config = types.GenerateContentConfig()

            analysis_prompt = f"""请分析以下网页内容，提取最重要的信息：

网址：{url}
网页内容：{content}

请提取：
1. 网页的主要内容或主题
2. 关键信息（如时间、价格、人物、事件等）
3. 如果是视频/文章，提取核心观点
4. 如果是商品页面，提取产品信息

要求：
- 用自然语言描述，不要有AI感
- 控制在100字以内
- 重点突出最有价值的信息
- 语气要自然，像朋友介绍一样

分析结果："""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=self.model,
                    contents=analysis_prompt,
                    config=config
                ),
                timeout=10.0
            )

            if response and hasattr(response, 'text') and response.text:
                analysis = response.text.strip()
                # 清理可能的格式符号
                analysis = re.sub(r'[🔍📋🔗📈💡⏰✅❌]', '', analysis)
                return analysis
            else:
                return "这个链接的内容好像比较复杂，没能完全分析出来"

        except Exception as e:
            logger.error(f"AI内容分析失败: {e}")
            return "链接内容分析遇到了一些问题"


class SmartSecondReplyAnalyzer:
    """智能二次回复分析器 - 根据内容复杂度决定是否触发二次回复"""

    def __init__(self, min_length: int = 500, complexity_threshold: float = 0.7, improvement_ratio: float = 2.0):
        self.min_length = min_length
        self.complexity_threshold = complexity_threshold
        self.improvement_ratio = improvement_ratio

    def _calculate_content_complexity(self, content: str) -> float:
        """计算内容复杂度"""
        if not content:
            return 0.0

        # 复杂度指标
        complexity_score = 0.0

        # 1. 长度因子 (0-0.3)
        length_score = min(len(content) / 2000, 0.3)
        complexity_score += length_score

        # 2. 结构化内容因子 (0-0.3)
        structure_indicators = ['###', '📋', '🔗', '📈', '💡', '⏰', '**', '*', '-', '1.', '2.', '3.']
        structure_count = sum(1 for indicator in structure_indicators if indicator in content)
        structure_score = min(structure_count / 10, 0.3)
        complexity_score += structure_score

        # 3. 信息密度因子 (0-0.2)
        words = content.split()
        unique_words = set(words)
        if words:
            density_score = min(len(unique_words) / len(words), 0.2)
            complexity_score += density_score

        # 4. 专业术语因子 (0-0.2)
        technical_terms = ['游戏', '战术', '玩法', '角色', '技能', '地图', '官方', '版本', '更新']
        tech_count = sum(1 for term in technical_terms if term in content)
        tech_score = min(tech_count / 20, 0.2)
        complexity_score += tech_score

        return min(complexity_score, 1.0)

    def should_trigger_second_reply(self, quick_result: str, deep_result: str) -> tuple[bool, str]:
        """判断是否应该触发二次回复"""
        if not deep_result or len(deep_result) < self.min_length:
            return False, "深度分析内容长度不足"

        # 计算内容复杂度
        deep_complexity = self._calculate_content_complexity(deep_result)
        quick_complexity = self._calculate_content_complexity(quick_result)

        # 计算内容改进比例
        length_improvement = len(deep_result) / max(len(quick_result), 1)

        # 判断是否触发
        should_trigger = (
            deep_complexity >= self.complexity_threshold or
            length_improvement >= self.improvement_ratio
        )

        reason = f"深度复杂度: {deep_complexity:.2f}, 长度改进: {length_improvement:.1f}x"

        return should_trigger, reason

    def should_enhance_result(self, quick_result: str, deep_result: str) -> tuple[bool, str, dict]:
        """判断是否应该增强结果，返回判断结果和统计信息"""
        should_trigger, reason = self.should_trigger_second_reply(quick_result, deep_result)

        # 计算详细统计信息
        stats = {
            "quick_length": len(quick_result),
            "deep_length": len(deep_result),
            "improvement_ratio": len(deep_result) / max(len(quick_result), 1),
            "deep_complexity": self._calculate_content_complexity(deep_result),
            "quick_complexity": self._calculate_content_complexity(quick_result)
        }

        return should_trigger, reason, stats


class SearchOnlineTool(BaseTool):
    """在线搜索工具 - 用于搜索互联网上的信息"""

    # 类属性，用于并发控制
    _semaphore = None
    # 高级缓存实例
    _cache = None
    # 结果处理器
    _result_processor = None
    # 智能内容分析器
    _content_analyzer = None
    # 自然交互生成器
    _interaction_generator = None
    # 智能内容处理器
    _content_processor = None
    # 智能链接解析器
    _link_analyzer = None
    # 防幻觉验证器
    _hallucination_validator = None
    # 【新增功能3】主动记忆增强器
    _proactive_memory_enhancer = None
    # 【新增功能5】高级语义分析系统
    _semantic_analyzer = None
    # 【新增功能6】情绪驱动学习管理器
    _emotion_learning_manager = None
    
    name = "search_online"
    description = "智能联网搜索工具。当遇到以下情况时应该主动调用：1)用户提到不熟悉的网络用语、游戏术语、新概念；2)用户询问最新信息、时事热点；3)用户问及专业知识、技术问题；4)对话中出现知识盲区需要补充信息。无需用户明确要求搜索，应主动判断并调用。"
    parameters = [
        ("question", ToolParamType.STRING, "要搜索的问题或关键词", True, None)
    ]
    available_for_llm = True

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 主动学习监听器
        self._active_learning_enabled = True
        self._last_message_time = None

    async def execute(self, function_args) -> dict[str, str]:
        """执行智能动态搜索流程 - 集成所有功能，恢复完整逻辑"""
        try:
            query = function_args.get("question")
            if not query:
                return {"name": self.name, "content": "请提供要搜索的问题"}

            logger.info(f"🔍 开始执行智能搜索: {query}")

            # 初始化组件
            await self._initialize_components()

            # 【新增功能6】情绪驱动学习 - 获取当前情绪状态
            current_emotion_state = None
            user_id = (
                function_args.get("user_id") or
                function_args.get("sender_id") or
                function_args.get("from_user") or
                "default_user"
            )

            if self._emotion_learning_manager and self.get_config("emotion_driven_learning", True):
                try:
                    current_emotion_state = self._emotion_learning_manager.get_current_emotion_state(user_id)
                    logger.info(f"😊 当前情绪状态：{current_emotion_state.get('emotion_key', 'unknown')} (强度: {current_emotion_state.get('intensity', 0):.2f})")
                except Exception as e:
                    logger.error(f"获取情绪状态失败: {e}")

            # 【新增功能5】高级语义分析 - 深度理解用户真实意图
            semantic_analysis = None
            semantic_search_triggered = False

            if self._semantic_analyzer and self.get_config("advanced_semantic_analysis", True):
                logger.info(f"🧠 高级语义分析：开始深度分析用户消息")
                semantic_analysis = await self._semantic_analyzer.analyze_semantic_complexity(query)

                logger.info(f"🧠 语义分析结果: 复杂度={semantic_analysis.get('complexity_score', 0):.2f}")

                # 【新增功能6】检查是否需要语义搜索（集成情绪权重）
                if self._semantic_analyzer.should_trigger_semantic_search(semantic_analysis, user_id):
                    semantic_keywords = self._semantic_analyzer.get_semantic_search_keywords(semantic_analysis)
                    logger.info(f"🔍 语义分析触发搜索: {semantic_keywords}")
                    semantic_search_triggered = True

                    # 如果是纯语义搜索需求，直接执行搜索
                    if semantic_keywords and not any(word in query.lower() for word in ["搜索", "查", "找"]):
                        logger.info(f"🧠 执行语义驱动的自动搜索")
                        semantic_query = " ".join(semantic_keywords[:2])  # 使用前2个关键词
                        semantic_result = await self._perform_intelligent_quick_search(semantic_query, {"semantic_driven": True})

                        # 结合语义分析调整回复风格
                        if semantic_result and "遇到问题" not in semantic_result and "失败" not in semantic_result:
                            adapted_result = self._semantic_analyzer._adapt_reply_with_semantic_analysis(semantic_result, semantic_analysis, query)
                            logger.info(f"🧠 语义驱动搜索成功，返回结果")
                            return {"name": self.name, "content": adapted_result}
                        else:
                            logger.info(f"🧠 语义驱动搜索失败，继续执行知识盲区检测和主动学习")

            # 【新增功能1】智能动态交互增强 - 检测知识盲区并自动联网
            knowledge_gaps_detected = False
            if self.get_config("intelligent_chat_enhancement", True):
                # 使用增强的权重分析器检测知识盲区
                enhanced_analysis = self._content_analyzer.complexity_analyzer.analyze_query_complexity(query, "")
                knowledge_gaps = enhanced_analysis.get("knowledge_gaps", {})

                if knowledge_gaps.get("has_gaps", False):
                    logger.info(f"🧠 智能动态交互：检测到知识盲区 - {knowledge_gaps.get('gap_types', [])}")
                    logger.info(f"🔍 智能动态交互：自动触发联网搜索增强回复")

                    # 设置知识盲区标记，供决策使用
                    self._current_knowledge_gaps = knowledge_gaps
                    knowledge_gaps_detected = True

            # 分析用户查询意图和权重
            query_intent = await self._content_analyzer.analyze_query_intent(query)

            # 如果检测到知识盲区，强制提高搜索权重
            if knowledge_gaps_detected:
                query_intent["force_search"] = True
                query_intent["knowledge_gaps"] = self._current_knowledge_gaps
                logger.info(f"🧠 智能动态交互：强制启用搜索模式")

            logger.info(f"查询意图分析: {query_intent}")

            # 快速搜索 (3-8秒内完成)
            if self.get_config("search.quick_search_enabled", True):
                # 【新增功能1】如果检测到知识盲区，使用增强搜索模式
                if knowledge_gaps_detected:
                    logger.info(f"🧠 智能动态交互：使用增强搜索模式填补知识盲区")
                    search_mode = "knowledge_gap_enhanced"
                else:
                    search_mode = "normal"

                logger.info(f"开始智能快速搜索: {query} (模式: {search_mode})")
                quick_result = await self._perform_intelligent_quick_search(query, query_intent)

                # 立即启动智能后台搜索（不阻塞返回）
                if self.get_config("search.background_search_enabled", True):
                    asyncio.create_task(self._intelligent_background_search(query, quick_result, query_intent))

                # 记录快速搜索行为
                await self._record_user_behavior(query, "intelligent_quick_search", quick_result)

                return {"name": self.name, "content": quick_result}

            # 如果快速搜索被禁用，执行标准搜索
            logger.info(f"执行标准搜索: {query}")
            standard_result = await self._search_knowledge(query)

            # 记录标准搜索行为
            await self._record_user_behavior(query, "standard_search", standard_result.get("content", ""))

            return standard_result

        except Exception as e:
            logger.error(f"搜索执行失败: {e}")
            return {"name": self.name, "content": f"搜索时出现错误: {str(e)}"}
            await self._initialize_components()

            # 【新增功能2】检测并优先处理用户分享的链接
            shared_links = []
            if self._link_analyzer and self.get_config("link_content_priority", True):
                shared_links = self._link_analyzer.detect_shared_links(query)
                if shared_links:
                    logger.info(f"检测到用户分享的链接: {shared_links}")
                    link_priority_result = await self._link_analyzer.priority_link_processing(query, shared_links)
                    if link_priority_result["has_priority_content"]:
                        logger.info(f"链接内容优先处理完成")
                        return {"name": self.name, "content": link_priority_result["content"]}

            # 【新增功能6】情绪驱动学习 - 获取当前情绪状态
            current_emotion_state = None
            user_id = (
                function_args.get("user_id") or
                function_args.get("sender_id") or
                function_args.get("from_user") or
                "default_user"
            )

            if self._emotion_learning_manager and self.get_config("emotion_driven_learning", True):
                try:
                    current_emotion_state = self._emotion_learning_manager.get_current_emotion_state(user_id)
                    logger.info(f"😊 当前情绪状态：{current_emotion_state.get('emotion_key', 'unknown')} (强度: {current_emotion_state.get('intensity', 0):.2f})")
                except Exception as e:
                    logger.error(f"获取情绪状态失败: {e}")

            # 【新增功能5】高级语义分析 - 深度理解用户真实意图
            semantic_analysis = None
            semantic_search_triggered = False

            if self._semantic_analyzer and self.get_config("advanced_semantic_analysis", True):
                logger.info(f"🧠 高级语义分析：开始深度分析用户消息")
                semantic_analysis = await self._semantic_analyzer.analyze_semantic_complexity(query)

                logger.info(f"🧠 语义分析结果: 复杂度={semantic_analysis.get('complexity_score', 0):.2f}")

                # 【新增功能6】检查是否需要语义搜索（集成情绪权重）
                if self._semantic_analyzer.should_trigger_semantic_search(semantic_analysis, user_id):
                    semantic_keywords = self._semantic_analyzer.get_semantic_search_keywords(semantic_analysis)
                    logger.info(f"🔍 语义分析触发搜索: {semantic_keywords}")
                    semantic_search_triggered = True

                    # 如果是纯语义搜索需求，直接执行搜索
                    if semantic_keywords and not any(word in query.lower() for word in ["搜索", "查", "找"]):
                        logger.info(f"🧠 执行语义驱动的自动搜索")
                        semantic_query = " ".join(semantic_keywords[:2])  # 使用前2个关键词
                        semantic_result = await self._perform_intelligent_quick_search(semantic_query, {"semantic_driven": True})

                        # 结合语义分析调整回复风格
                        if semantic_result:
                            adapted_result = self._semantic_analyzer._adapt_reply_with_semantic_analysis(semantic_result, semantic_analysis, query)
                            return {"name": self.name, "content": adapted_result}

            # 【新增功能1】智能动态交互增强 - 检测知识盲区并自动联网
            knowledge_gaps_detected = False
            if self.get_config("intelligent_chat_enhancement", True):
                # 使用增强的权重分析器检测知识盲区
                enhanced_analysis = self._content_analyzer.complexity_analyzer.analyze_query_complexity(query, "")
                knowledge_gaps = enhanced_analysis.get("knowledge_gaps", {})

                if knowledge_gaps.get("has_gaps", False):
                    logger.info(f"🧠 智能动态交互：检测到知识盲区 - {knowledge_gaps.get('gap_types', [])}")
                    logger.info(f"🔍 智能动态交互：自动触发联网搜索增强回复")

                    # 设置知识盲区标记，供决策使用
                    self._current_knowledge_gaps = knowledge_gaps
                    knowledge_gaps_detected = True

            # 分析用户查询意图和权重
            query_intent = await self._content_analyzer.analyze_query_intent(query)

            # 如果检测到知识盲区，强制提高搜索权重
            if knowledge_gaps_detected:
                query_intent["force_search"] = True
                query_intent["knowledge_gaps"] = self._current_knowledge_gaps
                logger.info(f"🧠 智能动态交互：强制启用搜索模式")

            logger.info(f"查询意图分析: {query_intent}")

            # 检查缓存
            if self.get_config("search.cache_enabled", True):
                cached_result = self._cache.get(query)
                if cached_result:
                    logger.info(f"缓存命中: {query}")
                    return {"name": self.name, "content": cached_result["content"]}

            # 快速搜索 (3-8秒内完成)
            if self.get_config("search.quick_search_enabled", True):
                # 【新增功能1】如果检测到知识盲区，使用增强搜索模式
                if knowledge_gaps_detected:
                    logger.info(f"🧠 智能动态交互：使用增强搜索模式填补知识盲区")
                    search_mode = "knowledge_gap_enhanced"
                else:
                    search_mode = "normal"

                logger.info(f"开始智能快速搜索: {query} (模式: {search_mode})")
                quick_result = await self._perform_intelligent_quick_search(query, query_intent)

                # 立即启动智能后台搜索（不阻塞返回）
                if self.get_config("search.background_search_enabled", True):
                    asyncio.create_task(self._intelligent_background_search(query, quick_result, query_intent))

                # 记录快速搜索行为
                await self._record_user_behavior(query, "intelligent_quick_search", quick_result)

                return {"name": self.name, "content": quick_result}
            else:
                # 传统搜索模式（兼容性）
                search_results = await self._search_knowledge(query)
                result = self._format_search_results(query, search_results)
                return {"name": self.name, "content": result}

        except Exception as e:
            logger.error(f"智能搜索执行失败: {e}")
            return {"name": self.name, "content": f"查询 '{query}' 遇到问题，稍等一下"}

    async def _perform_intelligent_quick_search(self, query: str, query_intent: dict) -> str:
        """执行智能快速搜索 - 基于意图优化"""
        try:
            timeout = self.get_config("search.quick_search_timeout", 5.0)

            # 根据查询意图调整搜索策略
            urgency = query_intent.get("urgency", 5)
            if urgency > 7:
                timeout = min(timeout, 3.0)  # 高紧急度缩短超时

            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            # 恢复之前工作的Google Generative AI配置
            genai.configure(api_key=api_key)
            model = genai.GenerativeModel(
                model_name=fallback_model,
                tools=["google_search_retrieval"]  # 使用之前工作的配置
            )

            # 根据查询类型构建优化的搜索提示
            search_prompt = self._build_intelligent_search_prompt(query, query_intent)

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    search_prompt
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"智能快速搜索成功: {query}, 响应长度: {len(result)}")
                return result
            else:
                return f"查询 '{query}' 没有结果"

        except asyncio.TimeoutError:
            logger.warning(f"智能快速搜索超时: {query}")
            return f"查询 '{query}' 超时了，稍等一下"
        except Exception as e:
            logger.error(f"智能快速搜索失败: {query} - {e}")
            return f"查询 '{query}' 遇到问题"

    def _build_intelligent_search_prompt(self, query: str, query_intent: dict) -> str:
        """根据查询意图构建智能搜索提示"""
        query_type = query_intent.get("type", "一般查询")
        depth = query_intent.get("depth", "简单")
        concepts = [c["concept"] for c in query_intent.get("concepts", [])]

        if query_type == "时间查询":
            return f"""关于{query}的时间：直接回答时间信息，不要说"搜索了"或"根据搜索"，就像朋友聊天一样直接说结果。有确切时间就说具体时间，没有就说"还没定"或"传言是XX时候"。"""

        elif query_type == "价格查询":
            return f"""关于{query}的价格：直接回答价格信息，不要说"搜索了"或"根据搜索"，就像朋友聊天一样直接说结果。有具体价格就说"XX元"，没有就说"还没公布价格"。"""

        elif query_type == "链接查询":
            return f"""关于{query}的链接：直接给出链接信息，不要说"搜索了"或"根据搜索"，就像朋友聊天一样直接说结果。找到就直接给链接，没找到就说"没有相关链接"。"""

        else:
            return f"""关于{query}：直接回答相关信息，不要说"搜索了"或"根据搜索"，就像朋友聊天一样直接说重点。用"是XX的哦"、"就是XX"、"XX那种"这样的自然表达。"""

    async def direct_execute(self, **function_args) -> str:
        '''
        直接调用联网搜索工具

        Args:
            question: 要进行搜索的问题或关键词

        Return:
            result: 模型返回的结果
        '''
        # 修正参数检查逻辑
        required_params = [p[0] for p in self.parameters if p[3]]  # 提取必填参数名
        missing = [p for p in required_params if p not in function_args]
        if missing:
            raise ValueError(f"工具类 {self.__class__.__name__} 缺少必要参数: {', '.join(missing)}")
        
        try:
            query = function_args.get("question")
            # 执行搜索逻辑
            search_results = await self._search_knowledge(query)
            return search_results.get("content", "")
        except Exception as e:
            logger.warning(f"执行搜索时发生异常: {e}")
            return ""

    async def _initialize_components(self):
        """初始化所有组件"""
        # 初始化高级缓存
        if self._cache is None:
            cache_config = self.get_config("search")
            self._cache = AdvancedSearchCache(
                max_size_mb=cache_config.get("cache_max_size_mb", 2.0),
                expire_minutes=cache_config.get("cache_expire_minutes", 3),
                max_entries=cache_config.get("cache_max_entries", 1000)
            )

        # 初始化结果处理器
        if self._result_processor is None:
            self._result_processor = SearchResultProcessor()

        # 初始化智能内容分析器
        if self._content_analyzer is None:
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            self._content_analyzer = IntelligentContentAnalyzer(api_key, fallback_model)

        # 初始化自然交互生成器
        if self._interaction_generator is None:
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            self._interaction_generator = NaturalInteractionGenerator(api_key, fallback_model)

        # 初始化智能内容处理器
        if self._content_processor is None:
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            self._content_processor = IntelligentContentProcessor(api_key, fallback_model)

        # 初始化智能链接解析器
        if self._link_analyzer is None:
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            self._link_analyzer = IntelligentLinkAnalyzer(api_key, fallback_model)

        # 初始化防幻觉验证器
        if self._hallucination_validator is None:
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            self._hallucination_validator = AntiHallucinationValidator(api_key, fallback_model)

        # 【新增功能3】初始化主动记忆增强器
        if self._proactive_memory_enhancer is None:
            if self.get_config("proactive_memory_enhancement", True):
                api_key = self.get_config("model.api_key")
                fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
                self._proactive_memory_enhancer = ProactiveMemoryEnhancer(api_key, fallback_model)

                # 配置主动记忆增强器
                self._proactive_memory_enhancer.enabled = self.get_config("proactive_memory_enhancement", True)
                self._proactive_memory_enhancer.search_interval_minutes = self.get_config("memory_search_interval_minutes", 30)
                self._proactive_memory_enhancer.max_searches_per_hour = self.get_config("max_proactive_searches_per_hour", 5)
                self._proactive_memory_enhancer.auto_detect_topics = self.get_config("auto_detect_interest_topics", True)
                self._proactive_memory_enhancer.background_search_during_chat = self.get_config("background_search_during_chat", True)

                logger.info(f"🧠 主动记忆增强器初始化完成:")
                logger.info(f"  - 启用状态: {self._proactive_memory_enhancer.enabled}")
                logger.info(f"  - 搜索间隔: {self._proactive_memory_enhancer.search_interval_minutes}分钟")
                logger.info(f"  - 每小时最大搜索次数: {self._proactive_memory_enhancer.max_searches_per_hour}")
                logger.info(f"  - 自动检测话题: {self._proactive_memory_enhancer.auto_detect_topics}")
                logger.info(f"  - 聊天时后台搜索: {self._proactive_memory_enhancer.background_search_during_chat}")
            else:
                logger.info(f"🧠 主动记忆增强器已禁用")

        # 【新增功能2】配置链接分析器的优先处理功能
        if self._link_analyzer:
            self._link_analyzer.link_priority_enabled = self.get_config("link_content_priority", True)
            self._link_analyzer.link_weight_boost = self.get_config("link_weight_boost", 1.5)
            self._link_analyzer.bypass_length_limit = self.get_config("bypass_length_limit_for_links", True)
            self._link_analyzer.link_processing_timeout = self.get_config("link_processing_timeout", 15)

        # 【新增功能4】配置自然交互生成器的动态风格功能
        if self._interaction_generator:
            self._interaction_generator.dynamic_style_enabled = self.get_config("dynamic_reply_style", True)
            self._interaction_generator.natural_chat_mode = self.get_config("natural_chat_mode", True)
            self._interaction_generator.formal_reply_suppression = self.get_config("formal_reply_suppression", True)
            self._interaction_generator.user_tone_analysis = self.get_config("user_tone_analysis", True)
            self._interaction_generator.reply_depth_adaptation = self.get_config("reply_depth_adaptation", True)
            self._interaction_generator.maintain_casual_expressions = self.get_config("maintain_casual_expressions", True)

        # 【新增功能5】初始化高级语义分析系统
        if self._semantic_analyzer is None:
            if self.get_config("advanced_semantic_analysis", True):
                api_key = self.get_config("model.api_key")
                fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
                self._semantic_analyzer = AdvancedSemanticAnalyzer(api_key, fallback_model)

                # 配置语义分析器
                self._semantic_analyzer.enabled = self.get_config("advanced_semantic_analysis", True)
                self._semantic_analyzer.analysis_depth = self.get_config("semantic_analysis_depth", "deep")
                self._semantic_analyzer.internet_slang_detection = self.get_config("internet_slang_detection", True)
                self._semantic_analyzer.irony_sarcasm_detection = self.get_config("irony_sarcasm_detection", True)
                self._semantic_analyzer.inappropriate_content_handling = self.get_config("inappropriate_content_handling", "smart")
                self._semantic_analyzer.gaming_terminology_analysis = self.get_config("gaming_terminology_analysis", True)
                self._semantic_analyzer.emotional_tone_analysis = self.get_config("emotional_tone_analysis", True)
                self._semantic_analyzer.contextual_understanding = self.get_config("contextual_understanding", True)
                self._semantic_analyzer.dynamic_learning_enabled = self.get_config("dynamic_learning_enabled", True)
                self._semantic_analyzer.semantic_search_trigger = self.get_config("semantic_search_trigger", True)
                self._semantic_analyzer.cultural_context_awareness = self.get_config("cultural_context_awareness", True)
                self._semantic_analyzer.language_style_adaptation = self.get_config("language_style_adaptation", True)

                logger.info(f"🧠 高级语义分析系统初始化完成:")
                logger.info(f"  - 启用状态: {self._semantic_analyzer.enabled}")
                logger.info(f"  - 分析深度: {self._semantic_analyzer.analysis_depth}")
                logger.info(f"  - 网络用语检测: {self._semantic_analyzer.internet_slang_detection}")
                logger.info(f"  - 反话讽刺检测: {self._semantic_analyzer.irony_sarcasm_detection}")
                logger.info(f"  - 游戏术语分析: {self._semantic_analyzer.gaming_terminology_analysis}")
                logger.info(f"  - 情感语调分析: {self._semantic_analyzer.emotional_tone_analysis}")
                logger.info(f"  - 动态学习机制: {self._semantic_analyzer.dynamic_learning_enabled}")
            else:
                logger.info(f"🧠 高级语义分析系统已禁用")

        # 【新增功能6】初始化情绪驱动学习管理器
        if self._emotion_learning_manager is None:
            if self.get_config("emotion_driven_learning", True):
                self._emotion_learning_manager = EmotionDrivenLearningManager()

                # 配置情绪学习管理器
                self._emotion_learning_manager.enabled = self.get_config("emotion_driven_learning", True)
                self._emotion_learning_manager.emotion_learning_sensitivity = self.get_config("emotion_learning_sensitivity", 0.7)
                self._emotion_learning_manager.positive_emotion_boost = self.get_config("positive_emotion_boost", 1.5)
                self._emotion_learning_manager.negative_emotion_reduction = self.get_config("negative_emotion_reduction", 0.5)
                self._emotion_learning_manager.emotion_threshold_adjustment = self.get_config("emotion_threshold_adjustment", True)
                self._emotion_learning_manager.emotion_cache_duration = self.get_config("emotion_cache_duration", 300)
                self._emotion_learning_manager.emotion_stability_check = self.get_config("emotion_stability_check", True)
                self._emotion_learning_manager.min_emotion_change_interval = self.get_config("min_emotion_change_interval", 60)

                # 将情绪管理器连接到语义分析器
                if self._semantic_analyzer:
                    self._semantic_analyzer.set_emotion_manager(self._emotion_learning_manager)

                logger.info(f"😊 情绪驱动学习管理器初始化完成:")
                logger.info(f"  - 启用状态: {self._emotion_learning_manager.enabled}")
                logger.info(f"  - 情绪敏感度: {self._emotion_learning_manager.emotion_learning_sensitivity}")
                logger.info(f"  - 积极情绪提升倍数: {self._emotion_learning_manager.positive_emotion_boost}")
                logger.info(f"  - 消极情绪降低倍数: {self._emotion_learning_manager.negative_emotion_reduction}")
                logger.info(f"  - 阈值动态调整: {self._emotion_learning_manager.emotion_threshold_adjustment}")
            else:
                logger.info(f"😊 情绪驱动学习管理器已禁用")

    async def _deep_search_and_analysis(self, query: str, quick_result: str):
        """深度搜索和分析的完整流程"""
        try:
            logger.info(f"开始深度搜索和分析: {query}")

            # 等待一段时间，让用户看到快速分析结果
            await asyncio.sleep(3.0)

            # 深度搜索阶段
            deep_search_results = await self._perform_deep_search(query)

            # 结果去重和合并
            if self.get_config("search.enable_deduplication", True):
                deep_search_results = self._result_processor.deduplicate_results(deep_search_results)

            # 深度分析阶段 (1-40秒)
            if self.get_config("search.enable_result_merge", True):
                final_result = self._result_processor.merge_results(quick_result, deep_search_results)
            else:
                final_result = await self._perform_deep_analysis_with_results(query, deep_search_results)

            # 缓存最终结果
            if self.get_config("search.cache_enabled", True):
                self._cache.set(query, {"content": final_result})

            # 用户行为记录和学习
            await self._record_user_behavior(query, "deep_analysis_complete", final_result)

            # 智能结果增强判断
            if self.get_config("search.enable_smart_second_reply", True):
                should_enhance, reason, stats = self._second_reply_analyzer.should_enhance_result(
                    quick_result, final_result
                )

                logger.info(f"结果增强判断: {should_enhance}")
                logger.info(f"统计信息: 快速长度={stats['quick_length']}, 深度长度={stats['deep_length']}, 改进比例={stats['improvement_ratio']:.1f}x, 深度复杂度={stats['deep_complexity']:.2f}")

                if should_enhance:
                    # 准备增强结果，供MaiBot系统使用
                    enhanced_result = self._prepare_enhanced_result(quick_result, final_result)

                    # 更新缓存为增强结果
                    if self.get_config("search.cache_enabled", True):
                        self._cache.set(f"enhanced_{query}", {"content": enhanced_result})

                    # 记录增强结果准备完成
                    await self._record_user_behavior(query, "result_enhanced", enhanced_result)

                    logger.info(f"结果已增强: {query}, 原长度={stats['quick_length']}, 增强后长度={len(enhanced_result)}")
                else:
                    logger.info(f"结果无需增强: {reason}")

            # 输出缓存统计
            cache_stats = self._cache.get_cache_stats()
            logger.info(f"缓存统计: {cache_stats}")

            logger.info(f"深度搜索和分析完成: {query}")

        except Exception as e:
            logger.error(f"深度搜索和分析失败: {query} - {e}")

    def _prepare_enhanced_result(self, quick_result: str, deep_result: str) -> str:
        """准备增强结果，供MaiBot系统使用"""
        try:
            # 提取深度分析的关键信息
            key_info = self._extract_key_information(deep_result)

            # 构建简洁的增强结果
            enhanced_result = f"{quick_result}\n\n💡 补充信息：{key_info}"

            return enhanced_result

        except Exception as e:
            logger.error(f"准备增强结果失败: {e}")
            return quick_result

    def _extract_key_information(self, deep_result: str) -> str:
        """从深度分析结果中提取关键信息"""
        try:
            # 简单的关键信息提取逻辑
            lines = deep_result.split('\n')
            key_lines = []

            for line in lines:
                line = line.strip()
                if line and (
                    '📋' in line or '🔗' in line or '📈' in line or
                    '💡' in line or '⏰' in line or '**' in line
                ):
                    # 清理格式符号，保留核心内容
                    clean_line = line.replace('📋', '').replace('🔗', '').replace('📈', '')
                    clean_line = clean_line.replace('💡', '').replace('⏰', '').replace('**', '')
                    clean_line = clean_line.strip(' :：')

                    if clean_line and len(clean_line) > 10:
                        key_lines.append(clean_line)

                        # 限制关键信息长度
                        if len(key_lines) >= 2:
                            break

            if key_lines:
                return ' | '.join(key_lines[:2])  # 最多2条关键信息
            else:
                # 如果没有找到格式化信息，提取前100字符
                clean_text = deep_result.replace('\n', ' ').strip()
                return clean_text[:100] + '...' if len(clean_text) > 100 else clean_text

        except Exception as e:
            logger.error(f"提取关键信息失败: {e}")
            return "深度分析已完成"

    async def _perform_quick_search(self, query: str) -> str:
        """执行快速搜索 - 3-8秒内完成"""
        try:
            timeout = self.get_config("search.quick_search_timeout", 5.0)

            # 配置模型
            api_key = self.get_config("model.api_key")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            client = genai.Client(api_key=api_key)
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config = types.GenerateContentConfig(tools=[grounding_tool])

            # 构建快速搜索提示
            quick_prompt = f"""快速搜索并简要回答（30字以内）：{query}

要求：
- 直接给出核心答案
- 如果是时间问题，给出具体时间
- 如果是链接问题，给出链接
- 如果暂无信息，说"搜索中..."
"""

            # 使用快速模型执行搜索
            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=fallback_model,
                    contents=quick_prompt,
                    config=config
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"快速搜索成功: {query}, 响应长度: {len(result)}")
                return result
            else:
                return f"查询 '{query}' 没有结果"

        except asyncio.TimeoutError:
            logger.warning(f"快速搜索超时: {query}")
            return f"查询 '{query}' 超时了，稍等一下"
        except Exception as e:
            logger.error(f"快速搜索失败: {query} - {e}")
            return f"查询 '{query}' 遇到问题"

    async def _background_deep_search(self, query: str, quick_result: str):
        """优化的后台深度搜索 - 快速简洁"""
        try:
            logger.info(f"开始优化深度搜索: {query}")

            # 缩短等待时间
            await asyncio.sleep(1.0)

            # 简化决策：直接基于快速结果长度判断
            if len(quick_result) > 50:  # 如果快速结果已经足够详细
                logger.info(f"快速结果已足够详细，跳过深度搜索: {query}")
                return

            # 执行单次深度搜索（不再多源搜索）
            deep_result = await self._perform_single_deep_search(query)

            if not deep_result:
                logger.info(f"深度搜索无结果: {query}")
                return

            # 智能处理内容（包含链接解析）
            if self.get_config("search.content_simplification_enabled", True):
                processed_result = await self._content_processor.process_content(query, deep_result, self._link_analyzer)
            else:
                processed_result = deep_result[:150] + '...' if len(deep_result) > 150 else deep_result

            # 判断是否需要@用户（降低阈值）
            should_mention = self._should_mention_user_simplified(quick_result, processed_result)

            if should_mention:
                await self._mention_user_with_results(query, processed_result)

            # 缓存处理后的结果
            if self.get_config("search.cache_enabled", True):
                self._cache.set(f"deep_{query}", {"content": processed_result})

            # 记录后台搜索行为
            await self._record_user_behavior(query, "background_search_optimized", processed_result)

            logger.info(f"优化深度搜索完成: {query}")

        except Exception as e:
            logger.error(f"优化深度搜索失败: {query} - {e}")

    async def _send_dynamic_replies(self, query: str, replies: List[str]):
        """发送动态回复"""
        try:
            if not replies:
                return

            # 获取最近活跃的聊天流
            chat_manager = get_chat_manager()
            recent_streams = list(chat_manager.last_messages.keys())

            if recent_streams:
                stream_id = recent_streams[-1]

                for i, reply in enumerate(replies):
                    # 间隔发送，模拟自然对话
                    if i > 0:
                        await asyncio.sleep(3 + i)  # 递增延迟

                    success = await send_api.text_to_stream(
                        text=reply,
                        stream_id=stream_id,
                        typing=False,
                        storage_message=True
                    )

                    if success:
                        logger.info(f"动态回复{i+1}发送成功: {reply}")
                    else:
                        logger.error(f"动态回复{i+1}发送失败: {reply}")
            else:
                logger.warning(f"无法获取聊天流，无法发送动态回复: {query}")

        except Exception as e:
            logger.error(f"发送动态回复失败: {query} - {e}")

    async def _send_single_reply(self, query: str, reply: str):
        """发送单次简洁回复"""
        try:
            if not reply:
                return

            # 获取最近活跃的聊天流
            chat_manager = get_chat_manager()
            recent_streams = list(chat_manager.last_messages.keys())

            if recent_streams:
                stream_id = recent_streams[-1]

                success = await send_api.text_to_stream(
                    text=reply,
                    stream_id=stream_id,
                    typing=False,
                    storage_message=True
                )

                if success:
                    logger.info(f"简洁回复发送成功: {reply}")
                else:
                    logger.error(f"简洁回复发送失败: {reply}")
            else:
                logger.warning(f"无法获取聊天流，无法发送回复: {query}")

        except Exception as e:
            logger.error(f"发送简洁回复失败: {query} - {e}")

    async def _intelligent_background_search(self, query: str, quick_result: str, query_intent: dict):
        """智能后台搜索 - 基于复杂度分析和防幻觉验证"""
        try:
            logger.info(f"开始智能后台搜索分析: {query}")

            # 等待一段时间，避免与快速搜索冲突
            await asyncio.sleep(1.0)

            # 分析查询复杂度，决定是否需要深度搜索
            complexity_analysis = self._content_analyzer.complexity_analyzer.analyze_query_complexity(
                query, quick_result
            )
            logger.info(f"查询复杂度分析: {complexity_analysis}")

            # 如果不需要深度搜索，直接返回
            if not complexity_analysis["needs_deep_search"]:
                logger.info(f"查询复杂度低，跳过深度搜索: {query} - {complexity_analysis['reason']}")
                return

            # 执行深度搜索
            deep_result = await self._perform_validated_deep_search(query, complexity_analysis)

            if not deep_result:
                logger.info(f"深度搜索无结果: {query}")
                return

            # 防幻觉验证
            validation_result = await self._hallucination_validator.validate_content(
                query, deep_result, deep_result
            )

            if not validation_result["is_valid"]:
                logger.warning(f"检测到幻觉内容: {query} - {validation_result['issues']}")
                # 使用修正后的内容
                processed_result = validation_result["corrected_content"]
            else:
                # 智能处理内容（包含链接解析）
                if self.get_config("search.content_simplification_enabled", True):
                    processed_result = await self._content_processor.process_content(query, deep_result, self._link_analyzer)
                else:
                    processed_result = deep_result[:150] + '...' if len(deep_result) > 150 else deep_result

            # 再次验证处理后的内容
            final_validation = await self._hallucination_validator.validate_content(
                query, deep_result, processed_result
            )

            if not final_validation["is_valid"]:
                logger.warning(f"处理后内容仍有问题: {query} - {final_validation['issues']}")
                processed_result = final_validation["corrected_content"]

            # 分析内容权重，决定是否需要多次回复
            weight_analysis = await self._content_analyzer.analyze_content_weight(processed_result, query_intent)
            logger.info(f"内容权重分析: {weight_analysis}")

            # 生成单次简洁回复
            concise_reply = await self._interaction_generator.generate_single_concise_reply(
                processed_result, weight_analysis, query
            )

            # 发送单次回复
            if concise_reply:
                await self._send_single_reply(query, concise_reply)

            # 缓存验证过的结果
            if self.get_config("search.cache_enabled", True):
                self._cache.set(f"validated_{query}", {
                    "content": processed_result,
                    "validation": final_validation,
                    "complexity": complexity_analysis
                })

            # 记录智能后台搜索行为
            await self._record_user_behavior(query, "validated_background_search", processed_result)

            logger.info(f"智能后台搜索完成: {query}")

        except Exception as e:
            logger.error(f"智能后台搜索失败: {query} - {e}")

    async def _perform_validated_deep_search(self, query: str, complexity_analysis: dict) -> str:
        """执行验证过的深度搜索"""
        try:
            timeout = self.get_config("search.background_search_timeout", 10.0)
            api_key = self.get_config("model.api_key")
            model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            # 恢复之前工作的Google Generative AI配置
            genai.configure(api_key=api_key)
            model_instance = genai.GenerativeModel(
                model_name=model,
                tools=["google_search_retrieval"]  # 使用之前工作的配置
            )

            # 根据复杂度级别调整搜索策略
            complexity_level = complexity_analysis["complexity_level"]

            if complexity_level == "simple":
                search_prompt = f"""关于{query}的补充信息：直接说重点，不要提"搜索"过程，就像朋友聊天一样。用"哦"、"就是"、"那种"这样的自然表达。"""

            elif complexity_level == "medium":
                search_prompt = f"""关于{query}的详细信息：直接说有用信息，不要提"搜索"过程，就像朋友聊天一样。多说点细节，但保持自然语气。"""

            else:  # complex
                search_prompt = f"""关于{query}的全面信息：直接详细回答，不要提"搜索"过程，就像朋友聊天一样。多角度说说，用自然的口语表达。"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model_instance.generate_content,
                    search_prompt
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"验证深度搜索成功: {query}, 复杂度: {complexity_level}, 响应长度: {len(result)}")
                return result
            else:
                return ""

        except Exception as e:
            logger.error(f"验证深度搜索失败: {query} - {e}")
            return ""

    async def _generate_validated_replies(self, content: str, weight_analysis: dict, query: str, original_search: str) -> List[str]:
        """生成验证过的回复"""
        try:
            # 先生成回复
            replies = await self._interaction_generator.generate_multiple_replies(
                content, weight_analysis, query
            )

            # 验证每个回复
            validated_replies = []
            for reply in replies:
                validation = await self._hallucination_validator.validate_content(
                    query, original_search, reply
                )

                if validation["is_valid"]:
                    validated_replies.append(reply)
                else:
                    logger.warning(f"回复验证失败: {reply} - {validation['issues']}")
                    # 使用修正后的回复
                    if validation["corrected_content"]:
                        validated_replies.append(validation["corrected_content"])

            return validated_replies

        except Exception as e:
            logger.error(f"验证回复生成失败: {e}")
            return []

    async def _perform_weighted_deep_search(self, query: str, query_intent: dict) -> str:
        """执行权重化深度搜索"""
        try:
            timeout = self.get_config("search.background_search_timeout", 10.0)
            api_key = self.get_config("model.api_key")
            model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            client = genai.Client(api_key=api_key)
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config = types.GenerateContentConfig(tools=[grounding_tool])

            # 根据查询意图构建深度搜索提示
            concepts = [c["concept"] for c in query_intent.get("concepts", [])]
            depth = query_intent.get("depth", "详细")

            search_prompt = f"""搜索关于"{query}"的补充信息：

重点关注：{concepts}
信息需求：{depth}

严格要求：
- 只提供确实存在的真实信息
- 如果信息不确定，明确标注"据说"、"可能"、"传言"
- 不要编造任何具体数据、时间、价格
- 如果没有找到相关信息，直接说"暂无更多信息"
- 保持客观中性，避免夸大描述
- 严格控制在100字以内
- 重点补充用户可能关心的实用信息"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=model,
                    contents=search_prompt,
                    config=config
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"权重化深度搜索成功: {query}, 响应长度: {len(result)}")
                return result
            else:
                return ""

        except Exception as e:
            logger.error(f"权重化深度搜索失败: {query} - {e}")
            return ""

    async def _perform_single_deep_search(self, query: str) -> str:
        """执行单次深度搜索 - 优化版本"""
        try:
            timeout = self.get_config("search.background_search_timeout", 10.0)
            api_key = self.get_config("model.api_key")
            # 直接使用快速模型，减少等待时间
            model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            client = genai.Client(api_key=api_key)
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config = types.GenerateContentConfig(tools=[grounding_tool])

            # 简化的搜索提示
            search_prompt = f"""快速搜索并简要补充信息：{query}

要求：
- 只提供最核心的补充信息
- 如果有具体时间/日期，优先提供
- 如果有官方消息，优先提供
- 控制在50字以内
"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=model,
                    contents=search_prompt,
                    config=config
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"单次深度搜索成功: {query}, 响应长度: {len(result)}")
                return result
            else:
                return ""

        except asyncio.TimeoutError:
            logger.warning(f"单次深度搜索超时: {query}")
            return ""
        except Exception as e:
            logger.error(f"单次深度搜索失败: {query} - {e}")
            return ""

    def _should_mention_user_simplified(self, quick_result: str, deep_result: str) -> bool:
        """简化的@用户判断逻辑"""
        try:
            if not deep_result or len(deep_result) < 20:
                return False

            # 简化判断条件
            # 1. 深度结果比快速结果长很多
            if len(deep_result) > len(quick_result) * 1.5:
                return True

            # 2. 包含关键信息词汇
            key_indicators = ['官方', '确认', '发布', '时间', '日期', '最新', '消息']
            if any(indicator in deep_result for indicator in key_indicators):
                return True

            # 3. 快速结果过于简短
            if len(quick_result) < 30 and len(deep_result) > 50:
                return True

            return False

        except Exception as e:
            logger.error(f"简化@用户判断失败: {e}")
            return False

    async def _try_models_with_priority(self, client, models: list, prompt: str, config, timeout: float):
        """按优先级尝试不同模型，失败时自动切换"""
        last_error = None

        for i, model_name in enumerate(models):
            try:
                logger.info(f"尝试模型 {i+1}/{len(models)}: {model_name}")

                response = await asyncio.wait_for(
                    asyncio.to_thread(
                        client.models.generate_content,
                        model=model_name,
                        contents=prompt,
                        config=config
                    ),
                    timeout=timeout
                )

                if response and hasattr(response, 'text') and response.text:
                    logger.info(f"模型 {model_name} 成功响应，长度: {len(response.text)}")
                    return response
                else:
                    logger.warning(f"模型 {model_name} 响应为空，尝试下一个模型")
                    continue

            except asyncio.TimeoutError as e:
                last_error = e
                logger.warning(f"模型 {model_name} 超时，尝试下一个模型")
                continue
            except Exception as e:
                last_error = e
                logger.warning(f"模型 {model_name} 失败: {type(e).__name__}: {str(e)}，尝试下一个模型")
                continue

        # 所有模型都失败了
        logger.error(f"所有模型都失败了，最后错误: {last_error}")
        raise last_error if last_error else Exception("所有模型都无法响应")

    async def _quick_analysis(self, query: str, presearch_result: Optional[str] = None) -> str:
        """快速分析 - 1-20秒内返回初步分析结果"""
        try:
            logger.info(f"开始快速分析: {query}")

            # 配置Google GenAI客户端
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            timeout = self.get_config("search.quick_timeout", 15.0)  # 增加到15秒

            # 创建客户端
            client = genai.Client(api_key=api_key)

            # 定义Google搜索工具
            grounding_tool = types.Tool(
                google_search=types.GoogleSearch()
            )

            # 配置生成设置
            config = types.GenerateContentConfig(
                tools=[grounding_tool]
            )

            # 构建快速分析提示
            quick_prompt = f"""作为专业的信息分析师，请对以下查询进行快速分析并提供初步回答：

查询：{query}

请按以下格式回答（控制在100字以内）：
🔍 初步分析：[基于搜索的核心信息]
📊 可信度：[高/中/低]
⏰ 信息时效：[最新/较新/需更新]

如果需要更详细信息，我将进行深度分析。"""

            # 执行快速分析 - 优先级模型选择
            response = await self._try_models_with_priority(
                client, [primary_model, fallback_model], quick_prompt, config, timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"快速分析成功: {query}, 响应长度: {len(result)}")

                # 启动深度分析（后台执行）
                asyncio.create_task(self._deep_analysis_and_reply(query))

                return result
            else:
                return f"🔍 正在分析 '{query}'，请稍候..."

        except asyncio.TimeoutError:
            logger.warning(f"快速分析超时: {query}")
            # 直接进行深度分析作为备用方案
            try:
                logger.info(f"快速分析超时，切换到深度分析: {query}")
                deep_result = await self._perform_deep_analysis(query)

                # 缓存深度分析结果
                if self.get_config("search.cache_enabled", True):
                    self._cache.set(query, {"content": deep_result})

                # 记录用户行为
                await self._record_user_behavior(query, "deep_analysis_fallback", deep_result)

                return deep_result
            except Exception as deep_error:
                logger.error(f"深度分析备用方案失败: {query} - {deep_error}")
                raise deep_error

        except Exception as e:
            logger.error(f"快速分析失败: {query} - {type(e).__name__}: {str(e)}")
            # 直接进行深度分析作为备用方案
            try:
                logger.info(f"快速分析失败，切换到深度分析: {query}")
                deep_result = await self._perform_deep_analysis(query)

                # 缓存深度分析结果
                if self.get_config("search.cache_enabled", True):
                    self._cache.set(query, {"content": deep_result})

                # 记录用户行为
                await self._record_user_behavior(query, "deep_analysis_fallback", deep_result)

                return deep_result
            except Exception as deep_error:
                logger.error(f"深度分析备用方案失败: {query} - {deep_error}")
                raise deep_error

    async def _deep_analysis_and_reply(self, query: str):
        """深度分析并发送最终回复"""
        try:
            logger.info(f"开始深度分析: {query}")

            # 等待一段时间，让用户看到快速分析结果
            await asyncio.sleep(3.0)

            # 执行深度分析
            deep_result = await self._perform_deep_analysis(query)

            # 缓存详细结果
            if self.get_config("search.cache_enabled", True):
                self._cache.set(query, {"content": deep_result})

            # 记录用户行为和学习
            await self._record_user_behavior(query, "deep_analysis", deep_result)

            # 发送详细分析结果（这里暂时只记录日志，实际发送需要MaiBot的消息系统支持）
            logger.info(f"深度分析完成: {query}")
            logger.info(f"深度分析结果: {deep_result[:200]}...")

        except Exception as e:
            logger.error(f"深度分析失败: {query} - {e}")

    async def _perform_deep_analysis(self, query: str) -> str:
        """执行深度分析"""
        try:
            # 配置Google GenAI客户端
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            timeout = self.get_config("search.deep_timeout", 30.0)

            # 创建客户端
            client = genai.Client(api_key=api_key)

            # 定义Google搜索工具
            grounding_tool = types.Tool(
                google_search=types.GoogleSearch()
            )

            # 配置生成设置
            config = types.GenerateContentConfig(
                tools=[grounding_tool]
            )

            # 构建深度分析提示
            deep_prompt = f"""作为专业的信息分析师，请对以下查询进行深度分析并提供详细回答：

查询：{query}

请按以下格式提供详细分析：
📋 详细信息：[完整的搜索结果和分析]
🔗 相关链接：[如果有的话]
📈 趋势分析：[相关趋势或发展]
💡 建议：[相关建议或注意事项]
⏰ 更新时间：[信息的时效性]

请提供准确、全面的信息。"""

            # 执行深度分析 - 优先级模型选择
            response = await self._try_models_with_priority(
                client, [primary_model, fallback_model], deep_prompt, config, timeout
            )

            if response and hasattr(response, 'text') and response.text:
                result = response.text.strip()
                logger.info(f"深度分析成功: {query}, 响应长度: {len(result)}")
                return result
            else:
                return f"深度分析暂时无法完成，请稍后再试。"

        except asyncio.TimeoutError:
            logger.warning(f"深度分析超时: {query}")
            return f"深度分析超时，请稍后再试。网络可能较慢。"
        except Exception as e:
            logger.error(f"深度分析执行失败: {query} - {type(e).__name__}: {str(e)}")
            return f"深度分析遇到问题: {type(e).__name__}"

    async def _record_user_behavior(self, query: str, analysis_type: str, result: str):
        """记录用户行为和学习"""
        try:
            # 记录查询信息
            behavior_data = {
                "timestamp": datetime.now().isoformat(),
                "query": query,
                "analysis_type": analysis_type,
                "result_length": len(result),
                "success": True
            }

            logger.info(f"用户行为记录: {behavior_data}")

            # 这里可以扩展为实际的学习机制
            # 例如：分析用户查询模式、优化搜索策略等

        except Exception as e:
            logger.error(f"用户行为记录失败: {e}")

    async def _deep_thinking_decision(self, query: str, quick_result: str) -> bool:
        """基于2.5 pro的深度思考决策是否需要深度搜索"""
        try:
            if not self.get_config("search.deep_thinking_enabled", True):
                return True  # 默认执行深度搜索

            timeout = self.get_config("search.thinking_timeout", 8.0)
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")

            client = genai.Client(api_key=api_key)
            config = types.GenerateContentConfig()  # 不使用搜索工具，只做思考

            thinking_prompt = f"""作为智能搜索决策系统，请判断是否需要进行深度搜索：

用户查询：{query}
快速搜索结果：{quick_result}

判断标准：
1. 快速结果是否已经充分回答了用户问题？
2. 用户是否需要更详细的信息？
3. 是否涉及复杂话题需要多源验证？

请只回答：需要 或 不需要
理由（一句话）："""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    client.models.generate_content,
                    model=primary_model,
                    contents=thinking_prompt,
                    config=config
                ),
                timeout=timeout
            )

            if response and hasattr(response, 'text') and response.text:
                decision_text = response.text.strip().lower()
                should_search = "需要" in decision_text
                logger.info(f"深度思考决策: {'需要' if should_search else '不需要'} - {response.text[:100]}")
                return should_search
            else:
                return True  # 默认执行

        except asyncio.TimeoutError:
            logger.warning(f"深度思考决策超时: {query}")
            return True  # 默认执行深度搜索
        except Exception as e:
            logger.error(f"深度思考决策失败: {query} - {type(e).__name__}: {str(e)}")
            return True  # 默认执行深度搜索

    async def _perform_background_search(self, query: str) -> List[Dict[str, Any]]:
        """执行后台深度搜索"""
        try:
            timeout = self.get_config("search.background_search_timeout", 20.0)
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")

            client = genai.Client(api_key=api_key)
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config = types.GenerateContentConfig(tools=[grounding_tool])

            # 构建后台搜索提示
            background_prompt = f"""深度搜索并提供详细信息：{query}

要求：
- 提供准确、详细的信息
- 包含相关链接和时间信息
- 多角度分析问题
"""

            # 使用模型优先级执行搜索
            response = await self._try_models_with_priority(
                client, [primary_model, fallback_model], background_prompt, config, timeout
            )

            if response and hasattr(response, 'text') and response.text:
                return [{
                    "content": response.text.strip(),
                    "source": "background_search",
                    "timestamp": datetime.now().isoformat()
                }]
            else:
                return []

        except Exception as e:
            logger.error(f"后台深度搜索执行失败: {e}")
            return []

    def _process_deep_results(self, deep_results: List[Dict[str, Any]]) -> str:
        """处理深度搜索结果"""
        if not deep_results:
            return ""

        # 合并所有结果
        combined_content = "\n\n".join([result["content"] for result in deep_results])
        return combined_content

    def _should_mention_user(self, quick_result: str, deep_result: str) -> bool:
        """判断是否应该@用户"""
        try:
            if not self.get_config("search.smart_mention_enabled", True):
                return False

            min_length = self.get_config("search.mention_min_length", 200)
            complexity_threshold = self.get_config("search.mention_complexity_threshold", 0.6)

            # 检查长度条件
            if len(deep_result) < min_length:
                return False

            # 检查复杂度条件
            if hasattr(self, '_second_reply_analyzer'):
                complexity = self._second_reply_analyzer._calculate_content_complexity(deep_result)
                if complexity < complexity_threshold:
                    return False

            # 检查改进程度
            improvement_ratio = len(deep_result) / max(len(quick_result), 1)
            if improvement_ratio < 2.0:
                return False

            logger.info(f"@用户条件满足: 长度={len(deep_result)}, 改进比例={improvement_ratio:.1f}x")
            return True

        except Exception as e:
            logger.error(f"@用户判断失败: {e}")
            return False

    async def _mention_user_with_results(self, query: str, deep_result: str):
        """@用户并发送深度搜索结果"""
        try:
            delay = self.get_config("search.mention_delay_seconds", 5)
            await asyncio.sleep(delay)

            # 格式化@用户消息 - 直接数据展示格式
            if deep_result:
                # 直接展示搜索结果，无conversational expressions
                mention_message = deep_result
            else:
                mention_message = "未找到更多相关信息"

            # 尝试获取最近活跃的聊天流
            chat_manager = get_chat_manager()
            recent_streams = list(chat_manager.last_messages.keys())

            if recent_streams:
                # 使用最近的聊天流（通常是当前正在进行的对话）
                stream_id = recent_streams[-1]  # 获取最后一个活跃的聊天流

                # 使用MaiBot的发送API发送消息
                success = await send_api.text_to_stream(
                    text=mention_message,
                    stream_id=stream_id,
                    typing=False,
                    storage_message=True
                )

                if success:
                    logger.info(f"成功@用户发送深度搜索结果: {query}, 长度: {len(mention_message)}")
                else:
                    logger.error(f"@用户消息发送失败: {query}")
            else:
                logger.warning(f"无法获取聊天流信息，无法@用户: {query}")

            # 记录@用户行为
            await self._record_user_behavior(query, "user_mentioned", mention_message)

        except Exception as e:
            logger.error(f"@用户失败: {query} - {e}")

    async def _perform_deep_search(self, query: str) -> List[Dict[str, Any]]:
        """执行深度搜索，获取多个搜索结果"""
        try:
            max_sources = self.get_config("search.max_search_sources", 5)
            search_results = []

            # 配置Google GenAI客户端
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            timeout = self.get_config("search.deep_timeout", 30.0)

            client = genai.Client(api_key=api_key)
            grounding_tool = types.Tool(google_search=types.GoogleSearch())
            config = types.GenerateContentConfig(tools=[grounding_tool])

            # 构建多个搜索查询
            search_queries = [
                f"{query} 最新信息",
                f"{query} 详细介绍",
                f"{query} 官方消息",
                f"{query} 发布时间",
                f"{query} 相关新闻"
            ]

            for i, search_query in enumerate(search_queries[:max_sources]):
                try:
                    logger.info(f"深度搜索 {i+1}/{max_sources}: {search_query}")

                    response = await self._try_models_with_priority(
                        client, [primary_model, fallback_model], search_query, config, timeout
                    )

                    if response and hasattr(response, 'text') and response.text:
                        search_results.append({
                            "query": search_query,
                            "content": response.text.strip(),
                            "source": f"深度搜索{i+1}",
                            "timestamp": datetime.now().isoformat()
                        })

                    # 避免频繁请求
                    await asyncio.sleep(1.0)

                except Exception as e:
                    logger.error(f"深度搜索失败 {search_query}: {e}")
                    continue

            logger.info(f"深度搜索完成: 获取到 {len(search_results)} 个结果")
            return search_results

        except Exception as e:
            logger.error(f"深度搜索执行失败: {e}")
            return []

    async def _perform_deep_analysis_with_results(self, query: str, search_results: List[Dict[str, Any]]) -> str:
        """基于搜索结果进行深度分析"""
        try:
            if not search_results:
                return f"深度分析：关于 '{query}' 的搜索未找到相关信息。"

            # 合并所有搜索结果
            combined_content = "\n\n".join([
                f"来源{i+1}: {result['content']}"
                for i, result in enumerate(search_results)
            ])

            # 配置分析模型
            api_key = self.get_config("model.api_key")
            primary_model = self.get_config("model.primary_model", "gemini-2.5-pro")
            fallback_model = self.get_config("model.fallback_model", "gemini-2.0-flash-exp")
            timeout = self.get_config("search.deep_timeout", 30.0)

            client = genai.Client(api_key=api_key)
            config = types.GenerateContentConfig()  # 不使用搜索工具，只做分析

            # 构建深度分析提示
            analysis_prompt = f"""基于以下搜索结果，对用户查询进行深度分析：

用户查询：{query}

搜索结果：
{combined_content}

请提供详细的分析报告，包括：
📋 核心信息总结
🔗 关键信息来源
📈 相关趋势分析
💡 结论和建议
⏰ 信息时效性评估

请确保信息准确、全面、有条理。"""

            response = await self._try_models_with_priority(
                client, [primary_model, fallback_model], analysis_prompt, config, timeout
            )

            if response and hasattr(response, 'text') and response.text:
                return response.text.strip()
            else:
                return f"深度分析：基于 {len(search_results)} 个搜索结果的分析暂时无法完成。"

        except Exception as e:
            logger.error(f"深度分析执行失败: {e}")
            return f"深度分析遇到问题: {type(e).__name__}"

    async def _search_knowledge_deep(self, query: str) -> dict:
        """深度搜索方法"""
        # 延迟初始化信号量
        if self._semaphore is None:
            max_concurrency = self.get_config('search.max_concurrency', 2)
            self._semaphore = asyncio.Semaphore(max_concurrency)

        # 获取深度搜索配置
        retry_attempts = self.get_config('search.deep_retry_attempts', 2)
        retry_wait_min = self.get_config('search.retry_wait_min', 2.0)
        retry_wait_max = self.get_config('search.retry_wait_max', 5.0)
        timeout = self.get_config('search.deep_timeout', 25.0)

        # 使用重试机制
        for attempt in range(1, retry_attempts + 1):
            try:
                async with self._semaphore:
                    return await self._execute_search(query, timeout)
            except (asyncio.TimeoutError, Exception) as e:
                if attempt < retry_attempts:
                    # 指数退避算法
                    wait_time = min(retry_wait_min * (2 ** (attempt - 1)), retry_wait_max)
                    logger.warning(f"深度搜索失败({attempt}/{retry_attempts})，等待{wait_time:.1f}秒后重试: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    if isinstance(e, asyncio.TimeoutError):
                        return {"content": "深度搜索请求超时"}
                    else:
                        return {"content": "深度搜索服务暂时不可用"}

    async def _search_knowledge(self, query: str) -> dict:
        """执行知识搜索"""
        # 延迟初始化信号量
        if self._semaphore is None:
            max_concurrency = self.get_config('search.max_concurrency', 5)
            self._semaphore = asyncio.Semaphore(max_concurrency)
        
        # 获取重试配置
        retry_attempts = self.get_config('search.retry_attempts', 3)
        retry_wait_min = self.get_config('search.retry_wait_min', 2.0)
        retry_wait_max = self.get_config('search.retry_wait_max', 10.0)
        timeout = self.get_config('search.timeout', 20.0)
        
        # 使用重试机制
        for attempt in range(1, retry_attempts + 1):
            try:
                async with self._semaphore:
                    return await self._execute_search(query, timeout)
            except (asyncio.TimeoutError, Exception) as e:
                if attempt < retry_attempts:
                    # 指数退避算法
                    wait_time = min(retry_wait_min * (2 ** (attempt - 1)), retry_wait_max)
                    logger.warning(f"搜索失败({attempt}/{retry_attempts})，等待{wait_time:.1f}秒后重试: {e}")
                    await asyncio.sleep(wait_time)
                else:
                    if isinstance(e, asyncio.TimeoutError):
                        return {"content": "搜索请求超时，请稍后再试"}
                    else:
                        return {"content": "搜索服务暂时不可用"}

    async def _execute_search(self, query: str, timeout: float) -> dict:
        """执行搜索的核心逻辑"""
        logger.info(f"正在执行搜索，搜索内容：{query}")
        content = f"""
你是一名专业的网络搜索专家，可以根据以下的要求精准的汇总出用户需要的信息，且一定能够保证信息的真实性和可靠性，汇总信息时不会带有个人主观色彩。
<doc>
这一段将详细为你说明所有内容块的用途，以及你如何通过他们精准的理解需求以优化你的回复。
<doc>：
该内容块是对提示文本规范化的解释说明，帮助你理解后文每个内容块的用处和解释。
<command>:
该内容块是对总体命令要求的阐述，也是你务必要严格遵守的内容。
<direction>:
该内容块包含对所需返回信息的方向的总体赘述。你需要根据该内容块中的内容调整你总结信息的方向。请注意，并不是所有的问题都一定完全契合该内容块中描述的方向。你应该根据自己的判断调整最后的输出内容，而不是完全死板的遵守该内容块中的内容。
<question>：
该内容块是你需要进行检索的核心问题或关键词。你最终输出的内容一定是围绕该内容块中的内容进行的，且尽量减少一切与该内容块中的内容无关的信息。
<context>：
该内容块包含一些必要的历史聊天信息。有时请求的question中指向性不明确，这时需要你通过提供的历史聊天信息来猜测出用户提出问题的具体内容。
<time>:
该内容块包含当前的详细的时间信息，帮助你选取更具有时效性的内容。
</doc>

<command>
请根据<question>和<context>中的内容，在网络上搜索相关内容，同时注意：
1.你的参考来源应该是可信的，权威的；
2.你不应该输出任何带有主观色彩的内容；
3.如果搜索的问题在网络上尚无定论，你应该将不同观念一起汇总出来；
4.当涉及到国家政治等敏感内容时，请务必保持绝对客观中立的角度，必要时你可以拒绝输出回答，并提醒用户保持严肃性；
5.你输出的内容应当足够简洁精炼，不要长篇大论，但要保留基本信息；
6.如果你无法给出一个可信的回答，就不要回答，禁止胡乱编造；
7.尽量选用最新的消息来源。
</command>

<direction>
{self.get_config('search.direction','请着重考虑与ACG文化、网络热梗、游戏术语、近期热点内容相关的方面。')}
</direction>

<question>
{query}
</question>

<context>
{self.get_messages_before(self.get_config('search.time_gap',270),self.get_config('search.max_limit',10))}
</context>

<time>
{time.strftime('%Y-%m-%d %H:%M', time.localtime())}
</time>
"""
        try:
            # 配置Google GenAI客户端
            api_key = self.get_config("model.api_key")
            model_name = self.get_config("model.model")

            logger.info(f"开始联网搜索: {query}")
            logger.debug(f"使用模型: {model_name}")

            client = genai.Client(api_key=api_key)

            # 定义Google搜索工具
            grounding_tool = types.Tool(
                google_search=types.GoogleSearch()
            )

            # 配置生成设置
            config = types.GenerateContentConfig(
                tools=[grounding_tool]
            )

            # 构建完整的提示内容
            full_prompt = f"""你是专业的网络搜索助手，请搜索并提供关于以下问题的最新、准确信息：

{content}

请提供详细的回答，并包含相关的网络来源链接。"""

            logger.debug(f"发送搜索请求，提示长度: {len(full_prompt)}")

            # 调用Gemini API进行联网搜索
            try:
                response = await asyncio.wait_for(
                    asyncio.to_thread(
                        client.models.generate_content,
                        model=model_name,
                        contents=full_prompt,
                        config=config
                    ),
                    timeout=timeout
                )
                logger.debug(f"API响应类型: {type(response)}")
                logger.debug(f"API响应内容: {response}")
            except Exception as api_error:
                logger.error(f"API调用异常: {type(api_error).__name__}: {str(api_error)}")
                logger.debug(f"API调用详细错误信息: {api_error}")
                raise api_error

            if response:
                logger.debug(f"响应对象类型: {type(response)}")
                logger.debug(f"响应对象属性: {dir(response)}")

                # 直接获取text属性
                response_text = None
                try:
                    if hasattr(response, 'text'):
                        response_text = response.text
                        logger.debug(f"直接获取text成功: {len(response_text) if response_text else 0}字符")

                    if not response_text and hasattr(response, 'candidates') and response.candidates:
                        # 备用方案：从candidates获取
                        candidate = response.candidates[0]
                        if hasattr(candidate, 'content') and candidate.content:
                            if hasattr(candidate.content, 'parts') and candidate.content.parts:
                                response_text = candidate.content.parts[0].text
                                logger.debug(f"从candidates获取text成功: {len(response_text) if response_text else 0}字符")
                except Exception as parse_error:
                    logger.error(f"解析响应时出错: {parse_error}")

                if response_text and response_text.strip():
                    logger.info(f"联网搜索成功: {query}, 响应长度: {len(response_text)}")
                    return {"content": response_text.strip()}
                else:
                    logger.error(f"搜索响应为空或无效: {query}")
                    logger.error(f"响应类型: {type(response)}")
                    logger.error(f"响应text属性: {getattr(response, 'text', 'None')}")
                    return {"content": f"抱歉，搜索 '{query}' 时获取到空响应。"}
            else:
                logger.error(f"搜索响应对象为None: {query}")
                return {"content": f"抱歉，搜索 '{query}' 时没有获取到响应对象。"}
            
        except asyncio.TimeoutError:
            logger.warning(f"搜索超时: {query}")
            raise
        except Exception as e:
            logger.error(f"搜索失败: {query} - {str(e)}")
            raise


    def _format_search_results(self, query: str, result: dict) -> str:
        """格式化搜索结果"""
        if not result:
            return f"未找到关于 '{query}' 的相关信息"

        formatted_text = f"📚 关于 '{query}' 的搜索结果:\n\n"
        
        content = result.get("content", "无摘要")
        formatted_text += f"   {content}\n"

        return formatted_text.strip()

    def get_messages_before(self, time_gap: int = 270, limit: int = 10):
        current = time.time()
        earlier = current - time_gap
        messages = message_api.get_messages_by_time(earlier,current,limit=limit)
        return message_api.build_readable_messages_to_str(messages)
        
# ===== 插件注册 =====

@register_plugin
class InternetSearchPlugin(BasePlugin):
    """InternetSearch插件 - 联网搜索插件"""

    # 插件基本信息
    plugin_name: str = "internet_search_plugin"  # 内部标识符
    enable_plugin: bool = True
    dependencies: List[str] = []  # 插件依赖列表
    python_dependencies: List[str] = ["google-genai", "tenacity"]  # 使用Google GenAI SDK
    config_file_name: str = "config.toml"  # 配置文件名

    # 配置节描述
    config_section_descriptions = {
        "plugin": "插件基本信息",
        "model": "大模型设置",
        "search": "搜索设置",
    }

    # 配置Schema定义
    config_schema: dict = {
        "plugin": {
            "name": ConfigField(
                type=str, default="internet_search_plugin", description="插件名称"
            ),
            "version": ConfigField(type=str, default="1.1.0", description="插件版本"),
            "config_version": ConfigField(type=str, default="1.2.0", description="配置文件版本"),
            "enabled": ConfigField(
                type=bool, default=False, description="是否启用插件"
            ),
        },
        "model": {
            "api_key": ConfigField(
                type=str, default="", description="你的Google Gemini API Key"
            ),
            "model": ConfigField(type=str, default="gemini-2.5-pro", description="使用的Gemini模型名称"),
        },
        "search":{
            "direction": ConfigField(
                type=str,
                default="请着重考虑与ACG文化、网络热梗、游戏术语、近期热点内容相关的方面。",
                description="描述模型应当着重考虑的搜索方向",
            ),
            "time_gap": ConfigField(
                type=int,
                default=270,
                description="提供最近多长时间内的聊天记录（秒）",
            ),
            "max_limit": ConfigField(
                type=int,
                default=10,
                description="提供最多多少条聊天记录（0为不限制）",
            ),
            # === 新增配置项 ===
            "timeout": ConfigField(
                type=float,
                default=20.0,
                description="API调用超时时间（秒）",
            ),
            "max_concurrency": ConfigField(
                type=int,
                default=5,
                description="最大并发搜索请求数",
            ),
            "retry_attempts": ConfigField(
                type=int,
                default=3,
                description="搜索失败时的重试次数",
            ),
            "retry_wait_min": ConfigField(
                type=float,
                default=2.0,
                description="重试之间的最小等待时间（秒）",
            ),
            "retry_wait_max": ConfigField(
                type=float,
                default=10.0,
                description="重试之间的最大等待时间（秒）",
            ),
            # ================
        }
    }

    def get_plugin_components(self) -> List[Tuple[ComponentInfo, Type]]:
        return [
            (SearchOnlineTool.get_tool_info(), SearchOnlineTool),
        ]


class ProactiveMemoryEnhancer:
    """主动记忆增强器 - 新增功能3"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model

        # 配置参数
        self.enabled = True
        self.search_interval_minutes = 30
        self.max_searches_per_hour = 5
        self.auto_detect_topics = True
        self.background_search_during_chat = True

        # 运行状态
        self.last_search_time = 0
        self.searches_this_hour = 0
        self.hour_start_time = time.time()
        self.user_interest_topics = set()
        self.chat_history_buffer = []
        self.proactive_cache = {}

        # 预定义关注话题
        self.default_topics = [
            "游戏更新", "新游戏发布", "技术新闻", "热门话题",
            "科技动态", "娱乐资讯", "社会热点"
        ]

    def add_chat_message(self, user_id: str, message: str):
        """添加聊天消息到历史缓冲区 - 新增功能3（增强日志）"""
        if not self.enabled:
            logger.debug(f"主动记忆增强未启用，跳过消息记录")
            return

        timestamp = time.time()
        self.chat_history_buffer.append({
            "user_id": user_id,
            "message": message,
            "timestamp": timestamp
        })

        logger.debug(f"📝 主动记忆增强：记录消息 - 用户: {user_id}, 消息: {message[:30]}..., 缓冲区大小: {len(self.chat_history_buffer)}")

        # 保持缓冲区大小
        if len(self.chat_history_buffer) > 100:
            self.chat_history_buffer = self.chat_history_buffer[-50:]
            logger.debug(f"缓冲区大小限制，保留最近50条消息")

        # 分析用户兴趣
        if self.auto_detect_topics:
            old_topics = self.user_interest_topics.copy()
            self._analyze_user_interests(message)
            if self.user_interest_topics != old_topics:
                logger.info(f"🎯 主动记忆增强：检测到新兴趣话题 - {self.user_interest_topics - old_topics}")

        # 特殊触发：如果消息包含"测试主动搜索"，立即触发（用于调试）
        if "测试主动搜索" in message:
            logger.info(f"🧪 主动记忆增强：检测到测试触发词，强制触发主动搜索")
            # 临时降低触发条件
            original_interval = self.search_interval_minutes
            self.search_interval_minutes = 0
            asyncio.create_task(self.perform_proactive_search(user_id))
            self.search_interval_minutes = original_interval

    def _analyze_user_interests(self, message: str):
        """分析用户兴趣话题 - 新增功能3"""
        message_lower = message.lower()

        # 游戏相关兴趣
        gaming_keywords = [
            "游戏", "玩家", "角色", "装备", "技能", "地图", "模式",
            "三角洲", "探员", "战术", "fps", "moba", "rpg"
        ]

        for keyword in gaming_keywords:
            if keyword in message_lower:
                self.user_interest_topics.add("游戏相关")
                break

        # 技术相关兴趣
        tech_keywords = [
            "技术", "编程", "代码", "开发", "软件", "硬件", "ai", "人工智能"
        ]

        for keyword in tech_keywords:
            if keyword in message_lower:
                self.user_interest_topics.add("技术相关")
                break

        # 娱乐相关兴趣
        entertainment_keywords = [
            "电影", "电视剧", "音乐", "明星", "综艺", "动漫", "小说"
        ]

        for keyword in entertainment_keywords:
            if keyword in message_lower:
                self.user_interest_topics.add("娱乐相关")
                break

    def should_trigger_proactive_search(self, user_id: str) -> bool:
        """判断是否应该触发主动搜索 - 新增功能3（优化触发条件）"""
        if not self.enabled or not self.background_search_during_chat:
            logger.debug(f"主动搜索未启用: enabled={self.enabled}, background_search={self.background_search_during_chat}")
            return False

        current_time = time.time()

        # 检查小时限制
        if current_time - self.hour_start_time > 3600:
            self.searches_this_hour = 0
            self.hour_start_time = current_time

        if self.searches_this_hour >= self.max_searches_per_hour:
            logger.debug(f"主动搜索达到小时限制: {self.searches_this_hour}/{self.max_searches_per_hour}")
            return False

        # 降低时间间隔要求（从30分钟降到10分钟）
        time_interval = min(self.search_interval_minutes * 60, 600)  # 最多10分钟
        if current_time - self.last_search_time < time_interval:
            remaining_time = time_interval - (current_time - self.last_search_time)
            logger.debug(f"主动搜索时间间隔未满足: 还需等待{remaining_time:.0f}秒")
            return False

        # 降低用户活跃度要求（从5分钟内3条消息降到10分钟内2条消息）
        recent_messages = [
            msg for msg in self.chat_history_buffer
            if msg["user_id"] == user_id and current_time - msg["timestamp"] < 600  # 10分钟内
        ]

        is_active = len(recent_messages) >= 2  # 10分钟内至少2条消息
        logger.debug(f"用户活跃度检查: {len(recent_messages)}条消息/10分钟, 需要>=2条, 结果: {is_active}")

        return is_active

    async def perform_proactive_search(self, user_id: str) -> dict:
        """执行主动搜索 - 新增功能3（增强日志）"""
        logger.info(f"🔍 主动记忆增强：开始执行主动搜索 - 用户ID: {user_id}")

        if not self.should_trigger_proactive_search(user_id):
            logger.info(f"❌ 主动记忆增强：不满足触发条件 - 用户ID: {user_id}")
            return {"performed": False, "reason": "不满足触发条件"}

        try:
            # 选择搜索话题
            search_topics = self._select_search_topics(user_id)
            logger.info(f"📋 主动记忆增强：选择搜索话题 - {search_topics}")

            if not search_topics:
                logger.warning(f"⚠️ 主动记忆增强：没有合适的搜索话题")
                return {"performed": False, "reason": "没有合适的搜索话题"}

            # 执行后台搜索
            search_results = []
            for topic in search_topics[:2]:  # 最多搜索2个话题
                try:
                    logger.info(f"🔎 主动记忆增强：开始搜索话题 - {topic}")
                    result = await self._background_search(topic)
                    if result:
                        search_results.append({
                            "topic": topic,
                            "content": result,
                            "timestamp": time.time()
                        })
                        logger.info(f"✅ 主动记忆增强：话题搜索成功 - {topic}: {result[:50]}...")
                    else:
                        logger.warning(f"⚠️ 主动记忆增强：话题搜索无结果 - {topic}")
                except Exception as e:
                    logger.error(f"❌ 主动记忆增强：话题搜索失败 - {topic}: {e}")
                    continue

            # 更新状态
            self.last_search_time = time.time()
            self.searches_this_hour += 1

            # 缓存结果
            for result in search_results:
                cache_key = f"proactive_{result['topic']}"
                self.proactive_cache[cache_key] = result
                logger.info(f"💾 主动记忆增强：缓存搜索结果 - {cache_key}")

            logger.info(f"🎉 主动记忆增强：搜索完成 - 用户ID: {user_id}, 话题数: {len(search_results)}")
            return {
                "performed": True,
                "topics_searched": len(search_results),
                "results": search_results
            }

        except Exception as e:
            logger.error(f"❌ 主动记忆增强：执行失败 - {e}")
            return {"performed": False, "reason": f"执行错误: {str(e)}"}

    def _select_search_topics(self, user_id: str) -> list:
        """选择搜索话题 - 新增功能3（优化选择逻辑）"""
        topics = []

        # 基于用户兴趣选择
        if self.user_interest_topics:
            topics.extend(list(self.user_interest_topics)[:2])
            logger.debug(f"基于用户兴趣选择话题: {topics}")

        # 添加默认热门话题
        if len(topics) < 2:
            import random
            remaining_slots = 2 - len(topics)
            available_topics = [t for t in self.default_topics if t not in topics]
            if available_topics:
                selected = random.sample(available_topics, min(remaining_slots, len(available_topics)))
                topics.extend(selected)
                logger.debug(f"添加默认话题: {selected}")

        # 确保至少有一个话题
        if not topics:
            topics = ["热门资讯"]
            logger.debug(f"使用备用话题: {topics}")

        logger.info(f"最终选择的搜索话题: {topics}")
        return topics

    async def _background_search(self, topic: str) -> str:
        """后台搜索实现 - 新增功能3"""
        try:
            import google.generativeai as genai
            from google.generativeai import types

            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel(self.model)

            search_prompt = f"""请搜索关于"{topic}"的最新信息和热门内容：

要求：
1. 提供最新的相关信息
2. 重点关注热门话题和趋势
3. 内容要有趣且有价值
4. 控制在100字以内
5. 用自然聊天的语气

直接回答相关内容，不要说"搜索了"等过程描述。"""

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    search_prompt
                ),
                timeout=10.0
            )

            if response and hasattr(response, 'text') and response.text:
                return response.text.strip()

            return ""

        except Exception as e:
            logger.error(f"后台搜索失败: {topic} - {e}")
            return ""

    def get_cached_proactive_content(self, topic: str) -> dict:
        """获取缓存的主动搜索内容 - 新增功能3"""
        cache_key = f"proactive_{topic}"
        if cache_key in self.proactive_cache:
            cached = self.proactive_cache[cache_key]
            # 检查缓存时效性（1小时）
            if time.time() - cached["timestamp"] < 3600:
                return cached
            else:
                del self.proactive_cache[cache_key]

        return None


class AdvancedSemanticAnalyzer:
    """高级内容语义分析系统 - 动态语义理解引擎"""

    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp"):
        self.api_key = api_key
        self.model = model

        # 配置参数
        self.enabled = True
        self.analysis_depth = "deep"
        self.internet_slang_detection = True
        self.irony_sarcasm_detection = True
        self.inappropriate_content_handling = "smart"
        self.gaming_terminology_analysis = True
        self.emotional_tone_analysis = True
        self.contextual_understanding = True
        self.dynamic_learning_enabled = True
        self.semantic_search_trigger = True
        self.cultural_context_awareness = True
        self.language_style_adaptation = True

        # 动态学习缓存
        self.semantic_patterns_cache = {}
        self.cultural_context_cache = {}
        self.language_evolution_tracker = {}

        # 分析历史
        self.analysis_history = []
        self.context_memory = []

        # 情绪驱动学习管理器
        self.emotion_manager = None

    def set_emotion_manager(self, emotion_manager):
        """设置情绪管理器"""
        self.emotion_manager = emotion_manager
        logger.info(f"😊 高级语义分析器：已连接情绪驱动学习管理器")

    async def analyze_semantic_complexity(self, message: str, context_history: list = None) -> dict:
        """深度语义分析 - 核心分析引擎"""
        if not self.enabled:
            return self._create_basic_analysis(message)

        try:
            # 构建分析上下文
            analysis_context = self._build_analysis_context(message, context_history)

            # 执行多维度语义分析
            semantic_analysis = await self._perform_deep_semantic_analysis(message, analysis_context)

            # 缓存分析结果用于动态学习
            if self.dynamic_learning_enabled:
                self._update_learning_cache(message, semantic_analysis)

            return semantic_analysis

        except Exception as e:
            logger.error(f"高级语义分析失败: {e}")
            return self._create_fallback_analysis(message)

    def _build_analysis_context(self, message: str, context_history: list = None) -> dict:
        """构建分析上下文"""
        context = {
            "current_message": message,
            "message_length": len(message),
            "has_punctuation": any(p in message for p in "！？。，；："),
            "has_emoji": any(ord(c) > 127 for c in message),
            "conversation_history": context_history or [],
            "recent_patterns": self._get_recent_patterns(),
            "cultural_indicators": self._detect_cultural_indicators(message)
        }

        return context

    async def _perform_deep_semantic_analysis(self, message: str, context: dict) -> dict:
        """执行深度语义分析"""
        # 构建智能分析提示
        analysis_prompt = self._create_semantic_analysis_prompt(message, context)

        try:
            import google.generativeai as genai

            genai.configure(api_key=self.api_key)
            model = genai.GenerativeModel(self.model)

            response = await asyncio.wait_for(
                asyncio.to_thread(
                    model.generate_content,
                    analysis_prompt
                ),
                timeout=8.0
            )

            if response and hasattr(response, 'text') and response.text:
                # 解析AI分析结果
                return self._parse_semantic_analysis_result(response.text, message, context)

            return self._create_fallback_analysis(message)

        except Exception as e:
            logger.error(f"深度语义分析执行失败: {e}")
            return self._create_fallback_analysis(message)

    def _create_semantic_analysis_prompt(self, message: str, context: dict) -> str:
        """创建语义分析提示词"""
        prompt = f"""作为高级语义分析专家，请深度分析以下用户消息的语言现象和真实意图：

用户消息："{message}"

请从以下维度进行分析：

1. **网络梗和流行语识别**：
   - 是否包含网络流行语、梗、表情包文字？
   - 这些表达的具体含义和文化背景是什么？

2. **反义词和讽刺表达**：
   - 用户是否在使用反话、讽刺或反义表达？
   - 真实意图与字面意思是否相反？

3. **情感色彩分析**：
   - 用户的真实情感倾向（积极/消极/中性）
   - 语气强度（轻松/认真/激动/平静）

4. **游戏术语和专业黑话**：
   - 是否包含特定领域的专业术语或俚语？
   - 这些术语的准确含义是什么？

5. **语境依赖理解**：
   - 这个表达是否需要结合上下文才能理解？
   - 可能的多重含义是什么？

6. **文化背景分析**：
   - 表达中的文化元素和背景知识
   - 是否涉及特定的文化现象或社会背景？

请以JSON格式返回分析结果：
{{
    "internet_slang": {{
        "detected": true/false,
        "terms": ["识别到的网络用语"],
        "meanings": ["对应含义"]
    }},
    "irony_sarcasm": {{
        "detected": true/false,
        "confidence": 0.0-1.0,
        "real_intent": "真实意图描述"
    }},
    "emotional_tone": {{
        "sentiment": "positive/negative/neutral",
        "intensity": 0.0-1.0,
        "tone_keywords": ["语气关键词"]
    }},
    "gaming_terminology": {{
        "detected": true/false,
        "terms": ["游戏术语"],
        "context": "游戏背景"
    }},
    "contextual_dependency": {{
        "needs_context": true/false,
        "ambiguity_level": 0.0-1.0,
        "possible_meanings": ["可能的含义"]
    }},
    "cultural_context": {{
        "cultural_elements": ["文化元素"],
        "background_knowledge": "背景知识描述"
    }},
    "true_intent": "用户的真实意图总结",
    "complexity_score": 0.0-1.0,
    "requires_search": true/false,
    "search_keywords": ["如果需要搜索的关键词"]
}}

分析要求：
- 深度理解，不要表面分析
- 考虑语言的动态性和创新性
- 识别隐含意思和文化内涵
- 判断是否需要联网搜索来理解新词汇"""

        return prompt

    def _parse_semantic_analysis_result(self, ai_response: str, message: str, context: dict) -> dict:
        """解析AI语义分析结果"""
        try:
            import json
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                analysis_result = json.loads(json_str)

                # 验证和补充分析结果
                validated_result = self._validate_analysis_result(analysis_result, message, context)

                logger.info(f"🧠 高级语义分析完成: 复杂度={validated_result.get('complexity_score', 0):.2f}")
                return validated_result

            # 如果无法解析JSON，使用文本分析
            return self._parse_text_analysis(ai_response, message, context)

        except Exception as e:
            logger.error(f"语义分析结果解析失败: {e}")
            return self._create_fallback_analysis(message)

    def _validate_analysis_result(self, result: dict, message: str, context: dict) -> dict:
        """验证和补充分析结果"""
        # 确保所有必需字段存在
        validated = {
            "internet_slang": result.get("internet_slang", {"detected": False, "terms": [], "meanings": []}),
            "irony_sarcasm": result.get("irony_sarcasm", {"detected": False, "confidence": 0.0, "real_intent": ""}),
            "emotional_tone": result.get("emotional_tone", {"sentiment": "neutral", "intensity": 0.5, "tone_keywords": []}),
            "gaming_terminology": result.get("gaming_terminology", {"detected": False, "terms": [], "context": ""}),
            "contextual_dependency": result.get("contextual_dependency", {"needs_context": False, "ambiguity_level": 0.0, "possible_meanings": []}),
            "cultural_context": result.get("cultural_context", {"cultural_elements": [], "background_knowledge": ""}),
            "true_intent": result.get("true_intent", message),
            "complexity_score": min(1.0, max(0.0, result.get("complexity_score", 0.5))),
            "requires_search": result.get("requires_search", False),
            "search_keywords": result.get("search_keywords", []),
            "analysis_timestamp": time.time(),
            "original_message": message
        }

        # 动态调整复杂度评分
        complexity_factors = 0
        if validated["internet_slang"]["detected"]:
            complexity_factors += 0.2
        if validated["irony_sarcasm"]["detected"]:
            complexity_factors += 0.3
        if validated["gaming_terminology"]["detected"]:
            complexity_factors += 0.2
        if validated["contextual_dependency"]["needs_context"]:
            complexity_factors += 0.2
        if len(validated["cultural_context"]["cultural_elements"]) > 0:
            complexity_factors += 0.1

        validated["complexity_score"] = min(1.0, max(validated["complexity_score"], complexity_factors))

        return validated

    def _parse_text_analysis(self, text_response: str, message: str, context: dict) -> dict:
        """从文本响应中解析分析结果"""
        # 基于文本内容的简化分析
        analysis = self._create_fallback_analysis(message)

        text_lower = text_response.lower()

        # 检测关键指标
        if any(word in text_lower for word in ["网络用语", "流行语", "梗", "表情包"]):
            analysis["internet_slang"]["detected"] = True
            analysis["complexity_score"] += 0.2

        if any(word in text_lower for word in ["反话", "讽刺", "反义", "真实意图"]):
            analysis["irony_sarcasm"]["detected"] = True
            analysis["complexity_score"] += 0.3

        if any(word in text_lower for word in ["游戏", "术语", "黑话", "专业"]):
            analysis["gaming_terminology"]["detected"] = True
            analysis["complexity_score"] += 0.2

        analysis["true_intent"] = message  # 保守处理
        return analysis

    def _get_recent_patterns(self) -> list:
        """获取最近的语言模式"""
        if len(self.analysis_history) < 5:
            return []

        recent_analyses = self.analysis_history[-5:]
        patterns = []

        for analysis in recent_analyses:
            if analysis.get("internet_slang", {}).get("detected", False):
                patterns.extend(analysis["internet_slang"].get("terms", []))
            if analysis.get("gaming_terminology", {}).get("detected", False):
                patterns.extend(analysis["gaming_terminology"].get("terms", []))

        return list(set(patterns))  # 去重

    def _detect_cultural_indicators(self, message: str) -> list:
        """检测文化指标"""
        indicators = []

        # 简单的文化指标检测
        if any(char in message for char in "哈哈呵呵嘿嘿"):
            indicators.append("humor_expression")

        if any(word in message for word in ["牛逼", "厉害", "6666", "666"]):
            indicators.append("praise_expression")

        if any(word in message for word in ["卧槽", "我去", "天哪"]):
            indicators.append("surprise_expression")

        return indicators

    def _update_learning_cache(self, message: str, analysis: dict):
        """更新动态学习缓存"""
        if not self.dynamic_learning_enabled:
            return

        # 记录分析历史
        self.analysis_history.append(analysis)
        if len(self.analysis_history) > 100:
            self.analysis_history = self.analysis_history[-50:]  # 保留最近50条

        # 更新语义模式缓存
        if analysis.get("internet_slang", {}).get("detected", False):
            terms = analysis["internet_slang"].get("terms", [])
            meanings = analysis["internet_slang"].get("meanings", [])
            for term, meaning in zip(terms, meanings):
                self.semantic_patterns_cache[term] = {
                    "meaning": meaning,
                    "frequency": self.semantic_patterns_cache.get(term, {}).get("frequency", 0) + 1,
                    "last_seen": time.time()
                }

        # 更新文化上下文缓存
        cultural_elements = analysis.get("cultural_context", {}).get("cultural_elements", [])
        for element in cultural_elements:
            if element not in self.cultural_context_cache:
                self.cultural_context_cache[element] = {
                    "frequency": 1,
                    "contexts": [message[:50]],
                    "last_seen": time.time()
                }
            else:
                self.cultural_context_cache[element]["frequency"] += 1
                self.cultural_context_cache[element]["last_seen"] = time.time()
                if len(self.cultural_context_cache[element]["contexts"]) < 5:
                    self.cultural_context_cache[element]["contexts"].append(message[:50])

    def _create_basic_analysis(self, message: str) -> dict:
        """创建基础分析结果"""
        return {
            "internet_slang": {"detected": False, "terms": [], "meanings": []},
            "irony_sarcasm": {"detected": False, "confidence": 0.0, "real_intent": message},
            "emotional_tone": {"sentiment": "neutral", "intensity": 0.5, "tone_keywords": []},
            "gaming_terminology": {"detected": False, "terms": [], "context": ""},
            "contextual_dependency": {"needs_context": False, "ambiguity_level": 0.0, "possible_meanings": [message]},
            "cultural_context": {"cultural_elements": [], "background_knowledge": ""},
            "true_intent": message,
            "complexity_score": 0.1,
            "requires_search": False,
            "search_keywords": [],
            "analysis_timestamp": time.time(),
            "original_message": message,
            "analysis_mode": "basic"
        }

    def _create_fallback_analysis(self, message: str) -> dict:
        """创建备用分析结果"""
        analysis = self._create_basic_analysis(message)
        analysis["analysis_mode"] = "fallback"

        # 基于简单规则的快速分析
        message_lower = message.lower()

        # 检测疑问语气
        if any(indicator in message for indicator in ["？", "?", "吗", "呢", "嘛"]):
            analysis["emotional_tone"]["tone_keywords"].append("questioning")
            analysis["complexity_score"] += 0.1

        # 检测感叹语气
        if any(indicator in message for indicator in ["！", "!", "哇", "哈"]):
            analysis["emotional_tone"]["tone_keywords"].append("exclamatory")
            analysis["emotional_tone"]["intensity"] = 0.7
            analysis["complexity_score"] += 0.1

        # 检测可能的网络用语
        internet_indicators = ["666", "yyds", "绝绝子", "emo", "破防", "内卷", "躺平"]
        for indicator in internet_indicators:
            if indicator in message_lower:
                analysis["internet_slang"]["detected"] = True
                analysis["internet_slang"]["terms"].append(indicator)
                analysis["complexity_score"] += 0.2
                break

        return analysis

    def should_trigger_semantic_search(self, analysis: dict, user_id: str = "default") -> bool:
        """判断是否应该触发语义搜索 - 集成情绪驱动学习"""
        if not self.semantic_search_trigger:
            return False

        base_complexity_score = analysis.get("complexity_score", 0)

        # 【新增】情绪驱动权重计算
        emotion_weight_result = {"adjusted_score": base_complexity_score, "threshold_adjustment": 0.0}
        if self.emotion_manager:
            try:
                emotion_state = self.emotion_manager.get_current_emotion_state(user_id)
                emotion_weight_result = self.emotion_manager.calculate_emotion_weight(emotion_state, base_complexity_score)

                logger.info(f"😊 情绪驱动搜索判断：{emotion_weight_result.get('emotion_influence', 'unknown')}")
            except Exception as e:
                logger.error(f"情绪权重计算失败: {e}")

        # 使用情绪调整后的复杂度评分
        adjusted_complexity_score = emotion_weight_result.get("adjusted_score", base_complexity_score)
        threshold_adjustment = emotion_weight_result.get("threshold_adjustment", 0.0)

        # 动态调整触发阈值
        base_threshold = 0.6
        adjusted_threshold = max(0.2, min(0.9, base_threshold + threshold_adjustment))

        logger.debug(f"😊 搜索阈值调整：{base_threshold:.2f} → {adjusted_threshold:.2f}")

        # 高复杂度内容触发搜索（使用调整后的阈值）
        if adjusted_complexity_score > adjusted_threshold:
            logger.info(f"😊 情绪驱动搜索触发：复杂度 {adjusted_complexity_score:.3f} > 阈值 {adjusted_threshold:.3f}")
            return True

        # 检测到未知网络用语触发搜索
        if analysis.get("internet_slang", {}).get("detected", False):
            unknown_terms = []
            for term in analysis["internet_slang"].get("terms", []):
                if term not in self.semantic_patterns_cache:
                    unknown_terms.append(term)
            if unknown_terms:
                logger.info(f"😊 未知网络用语触发搜索：{unknown_terms}")
                return True

        # 明确标记需要搜索
        if analysis.get("requires_search", False):
            logger.info(f"😊 明确标记触发搜索")
            return True

        logger.debug(f"😊 未触发语义搜索：复杂度 {adjusted_complexity_score:.3f} ≤ 阈值 {adjusted_threshold:.3f}")
        return False

    def get_semantic_search_keywords(self, analysis: dict) -> list:
        """获取语义搜索关键词"""
        keywords = []

        # 从分析结果中提取关键词
        if analysis.get("search_keywords"):
            keywords.extend(analysis["search_keywords"])

        # 添加未知的网络用语
        if analysis.get("internet_slang", {}).get("detected", False):
            for term in analysis["internet_slang"].get("terms", []):
                if term not in self.semantic_patterns_cache:
                    keywords.append(f"{term} 网络用语 含义")

        # 添加游戏术语
        if analysis.get("gaming_terminology", {}).get("detected", False):
            for term in analysis["gaming_terminology"].get("terms", []):
                keywords.append(f"{term} 游戏术语")

        return list(set(keywords))  # 去重

    def _adapt_reply_with_semantic_analysis(self, search_result: str, semantic_analysis: dict, original_query: str) -> str:
        """根据语义分析调整回复风格"""
        if not search_result or not semantic_analysis:
            return search_result

        try:
            # 获取用户的真实意图
            true_intent = semantic_analysis.get("true_intent", original_query)
            emotional_tone = semantic_analysis.get("emotional_tone", {})
            irony_sarcasm = semantic_analysis.get("irony_sarcasm", {})
            internet_slang = semantic_analysis.get("internet_slang", {})

            adapted_result = search_result

            # 1. 处理反话和讽刺
            if irony_sarcasm.get("detected", False) and irony_sarcasm.get("confidence", 0) > 0.7:
                logger.info(f"🎭 检测到讽刺表达，调整回复语气")
                # 如果用户在讽刺，回复也要相应调整语气
                if "不错" in search_result and emotional_tone.get("sentiment") == "negative":
                    adapted_result = search_result.replace("不错", "确实有点问题")
                elif "很好" in search_result and emotional_tone.get("sentiment") == "negative":
                    adapted_result = search_result.replace("很好", "确实不太行")

            # 2. 匹配用户的情感强度
            intensity = emotional_tone.get("intensity", 0.5)
            if intensity > 0.8:  # 用户很激动
                # 增加语气词
                if not any(word in adapted_result for word in ["哦", "呢", "的说", "那种"]):
                    adapted_result += "呢"
            elif intensity < 0.3:  # 用户很平静
                # 使用更平和的表达
                adapted_result = adapted_result.replace("！", "").replace("!", "")

            # 3. 融入网络用语风格
            if internet_slang.get("detected", False):
                slang_terms = internet_slang.get("terms", [])
                if "666" in slang_terms or "yyds" in slang_terms:
                    # 用户使用夸赞类网络用语，回复也可以更活泼
                    if "不错" in adapted_result:
                        adapted_result = adapted_result.replace("不错", "还挺6的")
                elif "emo" in slang_terms or "破防" in slang_terms:
                    # 用户情绪低落，回复要更温和
                    adapted_result = "嗯" + adapted_result

            # 4. 保持接地气的表达方式
            if not any(ending in adapted_result for ending in ["哦", "呢", "的", "那种", "就是"]):
                # 根据语义分析添加合适的语气词
                if emotional_tone.get("sentiment") == "positive":
                    adapted_result += "哦"
                elif emotional_tone.get("sentiment") == "neutral":
                    adapted_result += "那种"
                else:
                    adapted_result += "呢"

            logger.info(f"🎨 语义适配完成: {search_result[:30]}... -> {adapted_result[:30]}...")
            return adapted_result

        except Exception as e:
            logger.error(f"语义适配失败: {e}")
            return search_result


class EmotionDrivenLearningManager:
    """情绪驱动学习管理器 - 基于机器人心情状态的智能学习触发"""

    def __init__(self):
        # 配置参数
        self.enabled = True
        self.emotion_learning_sensitivity = 0.7
        self.positive_emotion_boost = 1.5
        self.negative_emotion_reduction = 0.5
        self.emotion_threshold_adjustment = True
        self.emotion_cache_duration = 300
        self.emotion_stability_check = True
        self.min_emotion_change_interval = 60

        # 情绪状态缓存
        self.current_emotion_state = None
        self.emotion_cache_timestamp = 0
        self.emotion_history = []
        self.last_emotion_change_time = 0

        # 情绪映射表
        self.emotion_mapping = {
            # 积极情绪
            "开心": {"type": "positive", "intensity": 0.8, "boost": 1.6},
            "兴奋": {"type": "positive", "intensity": 0.9, "boost": 1.8},
            "好奇": {"type": "positive", "intensity": 0.7, "boost": 1.4},
            "感兴趣": {"type": "positive", "intensity": 0.6, "boost": 1.3},
            "愉快": {"type": "positive", "intensity": 0.7, "boost": 1.4},
            "满意": {"type": "positive", "intensity": 0.5, "boost": 1.2},

            # 消极情绪
            "沮丧": {"type": "negative", "intensity": 0.7, "reduction": 0.4},
            "疲惫": {"type": "negative", "intensity": 0.8, "reduction": 0.3},
            "不耐烦": {"type": "negative", "intensity": 0.6, "reduction": 0.5},
            "烦躁": {"type": "negative", "intensity": 0.7, "reduction": 0.4},
            "无聊": {"type": "negative", "intensity": 0.5, "reduction": 0.6},
            "困惑": {"type": "negative", "intensity": 0.4, "reduction": 0.7},

            # 中性情绪
            "平静": {"type": "neutral", "intensity": 0.3, "factor": 1.0},
            "正常": {"type": "neutral", "intensity": 0.2, "factor": 1.0},
            "冷静": {"type": "neutral", "intensity": 0.3, "factor": 1.0},
        }

    def get_current_emotion_state(self, user_id: str = "default") -> dict:
        """获取当前情绪状态"""
        if not self.enabled:
            return self._create_neutral_emotion_state()

        try:
            # 检查缓存是否有效
            current_time = time.time()
            if (self.current_emotion_state and
                current_time - self.emotion_cache_timestamp < self.emotion_cache_duration):
                return self.current_emotion_state

            # 尝试从MaiBot情绪管理器获取情绪状态
            emotion_state = self._fetch_emotion_from_maibot(user_id)

            if emotion_state:
                # 更新缓存
                self.current_emotion_state = emotion_state
                self.emotion_cache_timestamp = current_time

                # 记录情绪历史
                self._record_emotion_history(emotion_state)

                logger.info(f"😊 情绪驱动学习：获取情绪状态 - {emotion_state.get('emotion_text', 'unknown')}")
                return emotion_state

            # 如果无法获取，返回中性情绪
            return self._create_neutral_emotion_state()

        except Exception as e:
            logger.error(f"获取情绪状态失败: {e}")
            return self._create_neutral_emotion_state()

    def _fetch_emotion_from_maibot(self, user_id: str) -> dict:
        """从MaiBot情绪管理器获取情绪状态"""
        try:
            # 尝试通过多种方式获取情绪状态
            emotion_text = self._get_emotion_from_system(user_id)

            if emotion_text:
                return self._parse_emotion_text(emotion_text)

            return None

        except Exception as e:
            logger.debug(f"从MaiBot获取情绪状态失败: {e}")
            return None

    def _get_emotion_from_system(self, user_id: str) -> str:
        """从系统获取情绪文本"""
        try:
            # 方法1: 尝试从全局变量或模块获取
            import sys

            # 检查是否有情绪管理器模块
            if hasattr(sys.modules.get('__main__', None), 'emotion_manager'):
                emotion_manager = sys.modules['__main__'].emotion_manager
                if hasattr(emotion_manager, 'get_current_emotion'):
                    emotion_info = emotion_manager.get_current_emotion(user_id)
                    if emotion_info:
                        return str(emotion_info)

            # 方法2: 尝试从日志中解析最近的情绪状态
            emotion_text = self._parse_emotion_from_logs()
            if emotion_text:
                return emotion_text

            # 方法3: 模拟情绪状态（用于测试）
            if user_id == "test_user":
                import random
                test_emotions = ["好奇", "平静", "感兴趣", "正常"]
                return f"感到{random.choice(test_emotions)}"

            return None

        except Exception as e:
            logger.debug(f"从系统获取情绪文本失败: {e}")
            return None

    def _parse_emotion_from_logs(self) -> str:
        """从日志中解析情绪状态（备用方案）"""
        try:
            # 这里可以实现从日志文件中读取最近的情绪状态
            # 由于日志格式复杂，这里提供一个简化的实现

            # 模拟从日志中解析到的情绪状态
            # 在实际实现中，这里应该读取真实的日志文件
            return None

        except Exception as e:
            logger.debug(f"从日志解析情绪状态失败: {e}")
            return None

    def _parse_emotion_text(self, emotion_text: str) -> dict:
        """解析情绪文本，提取情绪类型和强度"""
        try:
            emotion_text_lower = emotion_text.lower()

            # 查找匹配的情绪关键词
            matched_emotion = None
            max_confidence = 0

            for emotion_key, emotion_data in self.emotion_mapping.items():
                if emotion_key in emotion_text_lower:
                    confidence = len(emotion_key) / len(emotion_text_lower)  # 简单的匹配度计算
                    if confidence > max_confidence:
                        max_confidence = confidence
                        matched_emotion = {
                            "emotion_key": emotion_key,
                            "emotion_data": emotion_data,
                            "confidence": confidence
                        }

            if matched_emotion:
                emotion_data = matched_emotion["emotion_data"]
                return {
                    "emotion_text": emotion_text,
                    "emotion_key": matched_emotion["emotion_key"],
                    "emotion_type": emotion_data["type"],
                    "intensity": emotion_data["intensity"],
                    "confidence": matched_emotion["confidence"],
                    "learning_factor": self._calculate_learning_factor(emotion_data),
                    "threshold_adjustment": self._calculate_threshold_adjustment(emotion_data),
                    "timestamp": time.time()
                }

            # 如果没有匹配到具体情绪，尝试通过关键词推断
            return self._infer_emotion_from_keywords(emotion_text)

        except Exception as e:
            logger.error(f"解析情绪文本失败: {e}")
            return self._create_neutral_emotion_state()

    def _infer_emotion_from_keywords(self, emotion_text: str) -> dict:
        """通过关键词推断情绪状态"""
        emotion_text_lower = emotion_text.lower()

        # 积极关键词
        positive_keywords = ["高兴", "快乐", "喜欢", "满足", "激动", "期待", "惊喜"]
        # 消极关键词
        negative_keywords = ["难过", "失望", "生气", "焦虑", "担心", "害怕", "愤怒"]

        positive_score = sum(1 for keyword in positive_keywords if keyword in emotion_text_lower)
        negative_score = sum(1 for keyword in negative_keywords if keyword in emotion_text_lower)

        if positive_score > negative_score:
            return {
                "emotion_text": emotion_text,
                "emotion_key": "推断积极",
                "emotion_type": "positive",
                "intensity": min(0.8, positive_score * 0.3),
                "confidence": 0.5,
                "learning_factor": 1.2,
                "threshold_adjustment": -0.1,
                "timestamp": time.time()
            }
        elif negative_score > positive_score:
            return {
                "emotion_text": emotion_text,
                "emotion_key": "推断消极",
                "emotion_type": "negative",
                "intensity": min(0.8, negative_score * 0.3),
                "confidence": 0.5,
                "learning_factor": 0.7,
                "threshold_adjustment": 0.1,
                "timestamp": time.time()
            }

        return self._create_neutral_emotion_state()

    def _calculate_learning_factor(self, emotion_data: dict) -> float:
        """计算学习因子"""
        emotion_type = emotion_data.get("type", "neutral")
        intensity = emotion_data.get("intensity", 0.5)

        if emotion_type == "positive":
            base_boost = emotion_data.get("boost", self.positive_emotion_boost)
            return min(2.0, base_boost * (1 + intensity * 0.5))
        elif emotion_type == "negative":
            base_reduction = emotion_data.get("reduction", self.negative_emotion_reduction)
            return max(0.1, base_reduction * (1 - intensity * 0.3))
        else:
            return 1.0

    def _calculate_threshold_adjustment(self, emotion_data: dict) -> float:
        """计算阈值调整值"""
        if not self.emotion_threshold_adjustment:
            return 0.0

        emotion_type = emotion_data.get("type", "neutral")
        intensity = emotion_data.get("intensity", 0.5)

        if emotion_type == "positive":
            # 积极情绪降低触发阈值，更容易触发学习
            return -0.2 * intensity * self.emotion_learning_sensitivity
        elif emotion_type == "negative":
            # 消极情绪提高触发阈值，减少学习触发
            return 0.2 * intensity * self.emotion_learning_sensitivity
        else:
            return 0.0

    def _create_neutral_emotion_state(self) -> dict:
        """创建中性情绪状态"""
        return {
            "emotion_text": "正常",
            "emotion_key": "正常",
            "emotion_type": "neutral",
            "intensity": 0.3,
            "confidence": 1.0,
            "learning_factor": 1.0,
            "threshold_adjustment": 0.0,
            "timestamp": time.time()
        }

    def calculate_emotion_weight(self, emotion_state: dict, base_complexity_score: float) -> dict:
        """计算情绪权重，调整复杂度评分"""
        if not self.enabled or not emotion_state:
            return {
                "adjusted_score": base_complexity_score,
                "emotion_factor": 1.0,
                "threshold_adjustment": 0.0,
                "emotion_influence": "none"
            }

        try:
            learning_factor = emotion_state.get("learning_factor", 1.0)
            threshold_adjustment = emotion_state.get("threshold_adjustment", 0.0)
            emotion_type = emotion_state.get("emotion_type", "neutral")
            intensity = emotion_state.get("intensity", 0.5)

            # 计算调整后的复杂度评分
            adjusted_score = base_complexity_score * learning_factor
            adjusted_score = max(0.0, min(1.0, adjusted_score))  # 确保在合理范围内

            # 确定情绪影响类型
            if emotion_type == "positive" and learning_factor > 1.1:
                emotion_influence = f"积极促进 (+{(learning_factor-1)*100:.0f}%)"
            elif emotion_type == "negative" and learning_factor < 0.9:
                emotion_influence = f"消极抑制 (-{(1-learning_factor)*100:.0f}%)"
            else:
                emotion_influence = "中性影响"

            result = {
                "adjusted_score": adjusted_score,
                "emotion_factor": learning_factor,
                "threshold_adjustment": threshold_adjustment,
                "emotion_influence": emotion_influence,
                "emotion_type": emotion_type,
                "emotion_intensity": intensity
            }

            logger.info(f"😊 情绪权重计算：{base_complexity_score:.3f} → {adjusted_score:.3f} ({emotion_influence})")
            return result

        except Exception as e:
            logger.error(f"计算情绪权重失败: {e}")
            return {
                "adjusted_score": base_complexity_score,
                "emotion_factor": 1.0,
                "threshold_adjustment": 0.0,
                "emotion_influence": "计算失败"
            }
