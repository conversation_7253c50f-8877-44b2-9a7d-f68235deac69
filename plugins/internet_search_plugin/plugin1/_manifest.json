{"manifest_version": 1, "name": "麦麦联网搜索插件", "version": "1.1.0", "description": "基于Gemini API的智能联网搜索插件，包含50个完整功能模块，支持情绪分析、语义理解、个性化学习等高级功能", "author": {"name": "AI Assistant", "url": "https://github.com/ai-assistant"}, "license": "GPL-v3.0-or-later", "host_application": {"min_version": "0.10.0", "max_version": "0.10.1"}, "homepage_url": "https://github.com/ai-assistant/InternetSearchPlugin", "repository_url": "https://github.com/ai-assistant/InternetSearchPlugin", "keywords": ["tool", "search", "online", "gemini", "ai", "intelligent", "emotion", "semantic", "personalization"], "categories": ["Internet", "Search", "AI", "Intelligence"], "default_locale": "zh-CN", "locales_path": "_locales", "plugin_info": {"is_built_in": false, "components": [{"type": "tool", "name": "search_online", "description": "智能联网搜索工具，具备情绪感知、语义分析、主动学习等50个完整功能"}], "features": ["基于Gemini API的真实联网搜索", "智能触发机制（TF-IDF算法）", "深度情绪分析（5种情绪×3个强度级别）", "高级语义理解（网络用语、游戏术语、反话检测）", "个性化学习系统（协同过滤、强化学习）", "智能缓存系统（TTL缓存管理）", "多模式搜索（简单、详细、复杂）", "用户行为分析和画像构建", "安全控制和频率限制", "完整的监控统计功能"], "algorithms": ["TF-IDF重要性计算", "Jaccard语义相似度", "多维度情绪分析", "协同过滤推荐", "Q-learning策略优化", "语义向量构建", "热度衰减机制"], "function_modules": {"core_search": 4, "emotion_intelligence": 4, "semantic_analysis": 6, "interaction_enhancement": 4, "active_learning": 4, "data_analysis": 4, "reply_optimization": 4, "cache_performance": 4, "security_control": 4, "monitoring_statistics": 4, "personalization": 4, "configuration_management": 4, "total": 50}}}